# متطلبات نظام إدارة الصيدلية
# Pharmacy Management System Requirements

# قاعدة البيانات - Database
mysql-connector-python>=8.0.0

# واجهة المستخدم - GUI (مثبت مع Python عادة)
# tkinter (built-in with Python)

# معالجة التواريخ والأوقات - Date/Time handling (مثبت مع Python)
# datetime (built-in with Python)

# التعبيرات النمطية - Regular expressions (مثبت مع Python)
# re (built-in with Python)

# نظام التشغيل - Operating system interface (مثبت مع Python)
# os (built-in with Python)

# العمليات الفرعية - Subprocess management (مثبت مع Python)
# subprocess (built-in with Python)

# معلومات النظام - System information (مثبت مع Python)
# platform (built-in with Python)

# ملاحظات التثبيت:
# 1. ت<PERSON><PERSON>د من تثبيت Python 3.6 أو أحدث
# 2. ت<PERSON>ك<PERSON> من تثبيت وتشغيل MySQL Server
# 3. قم بتشغيل: pip install -r requirements.txt
# 4. قم بتشغيل: python setup.py للإعداد الأولي
# 5. قم بتشغيل: python main.py لتشغيل النظام

# Installation Notes:
# 1. Ensure Python 3.6+ is installed
# 2. Ensure MySQL Server is installed and running
# 3. Run: pip install -r requirements.txt
# 4. Run: python setup.py for initial setup
# 5. Run: python main.py to start the system
