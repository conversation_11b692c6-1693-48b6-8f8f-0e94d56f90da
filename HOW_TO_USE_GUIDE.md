# 📋 دليل الاستخدام السريع - صيدلية الشفاء

## 🚀 **تشغيل النظام:**
```bash
python main.py
```

## 🔐 **تسجيل الدخول:**
```
👑 المدير: admin / admin123 (جميع الوظائف)
👤 المستخدم: user / user123 (وظائف محدودة)
```

## 📦 **كيفية إضافة منتج جديد:**

### **الخطوات:**
1. **سجل دخول كمدير** → admin / admin123
2. **اضغط "إدارة المخزون"**
3. **اضغط "+ إضافة منتج جديد"**
4. **املأ البيانات:**
   - **اسم المنتج:** مثل "باراسيتامول"
   - **الفئة:** مثل "مسكن" (افتراضي: "دواء")
   - **سعر الشراء:** مثل "50.00" (افتراضي: "0.00")
   - **سعر البيع:** مثل "75.00" (افتراضي: "0.00")
   - **الكمية:** مثل "100" (افتراضي: "1")
   - **تاريخ الانتهاء:** مثل "2025-12-31" (افتراضي: "2025-12-31")
5. **اضغط "حفظ المنتج"**
6. **سيظهر المنتج في الجدول فوراً**

### **نصائح مهمة:**
- ✅ **اسم المنتج مطلوب** ولا يمكن تكراره
- ✅ **الأسعار والكمية** يجب أن تكون أرقام موجبة
- ✅ **تاريخ الانتهاء** اختياري بصيغة YYYY-MM-DD
- ✅ **سيحذرك النظام** إذا كان سعر البيع أقل من سعر الشراء

## 🛒 **كيفية إتمام عملية بيع:**

### **الخطوات:**
1. **اضغط "نظام المبيعات"**
2. **في الجانب الأيسر (اختيار المنتجات):**
   - ابحث عن المنتج أو اختر من القائمة
   - أدخل الكمية المطلوبة (افتراضي: 1)
   - اضغط "إضافة للسلة"
3. **كرر للمنتجات الأخرى**
4. **في الجانب الأيمن (السلة والدفع):**
   - راجع المنتجات في السلة
   - تأكد من الإجمالي
   - أدخل اسم العميل (اختياري)
   - أدخل رقم الهاتف (اختياري)
   - اختر طريقة الدفع (نقد، بنكلي، مصرفي، سداد، BIC Bank، Click)
5. **اضغط "إتمام البيع وطباعة الفاتورة"**
6. **ستطبع الفاتورة في الطرفية**
7. **سيتم تحديث المخزون تلقائياً**

### **وظائف السلة:**
- ✅ **حذف من السلة:** اختر المنتج واضغط "حذف من السلة"
- ✅ **مسح السلة:** اضغط "مسح السلة" لحذف جميع المنتجات
- ✅ **الإجمالي:** يحسب تلقائياً مع كل تغيير

### **نصائح مهمة:**
- ✅ **فقط المنتجات المتاحة** (الكمية > 0) تظهر للبيع
- ✅ **النظام يتحقق** من الكمية المتاحة قبل الإضافة
- ✅ **رقم فاتورة فريد** يتم إنشاؤه تلقائياً
- ✅ **المخزون يتحدث فوراً** بعد البيع

## 📊 **عرض التقارير:**

### **تقرير المبيعات:**
1. **من نظام المبيعات** اضغط "📊 تقرير المبيعات"
2. **ستظهر إحصائيات:**
   - مبيعات اليوم (عدد الفواتير والمبلغ)
   - مبيعات الشهر الحالي
   - إجمالي المبيعات منذ البداية

### **إدارة الفواتير (للمدير فقط):**
1. **اضغط "إدارة الفواتير"**
2. **ستظهر جميع الفواتير مع:**
   - رقم الفاتورة
   - العميل والهاتف
   - الإجمالي وطريقة الدفع
   - التاريخ والوقت
3. **للبحث:** استخدم حقول البحث (رقم الفاتورة، العميل، التاريخ)
4. **لعرض التفاصيل:** انقر نقراً مزدوجاً على أي فاتورة
5. **للطباعة:** اضغط "🖨️ طباعة تقرير شامل"

## 👥 **إدارة المستخدمين (للمدير فقط):**

### **إضافة مستخدم جديد:**
1. **اضغط "إدارة المستخدمين"**
2. **اضغط "+ إضافة مستخدم جديد"**
3. **املأ البيانات:**
   - اسم المستخدم (3 أحرف على الأقل)
   - كلمة المرور (6 أحرف على الأقل)
   - تأكيد كلمة المرور
   - الصلاحية (مستخدم محدود / مدير النظام)
4. **اضغط "إضافة المستخدم"**

### **تعديل مستخدم:**
1. **انقر نقراً مزدوجاً على المستخدم**
2. **يمكنك تغيير:**
   - كلمة المرور (اتركها فارغة للاحتفاظ بالحالية)
   - الصلاحية
3. **اضغط "حفظ التغييرات"** أو **"حذف المستخدم"**

### **نصائح مهمة:**
- ✅ **لا يمكن تعديل** حسابك الشخصي من هنا
- ✅ **لا يمكن حذف** حسابك الشخصي
- ✅ **أسماء المستخدمين** يجب أن تكون فريدة

## 🔧 **اختبار النظام:**

### **اختبار قاعدة البيانات:**
1. **اضغط "اختبار قاعدة البيانات"**
2. **ستظهر:**
   - حالة الاتصال (نجح/فشل)
   - عدد المستخدمين في قاعدة البيانات
   - رسائل خطأ إذا فشل الاتصال

## 🚪 **تسجيل الخروج:**
1. **اضغط "تسجيل خروج"**
2. **أكد الخروج**
3. **ستعود لشاشة تسجيل الدخول**

## ⚠️ **نصائح مهمة للاستخدام:**

### **للمدير:**
- ✅ **أضف المنتجات أولاً** قبل البدء في المبيعات
- ✅ **راجع التقارير بانتظام** لمتابعة الأداء
- ✅ **أنشئ مستخدمين محدودين** للموظفين
- ✅ **احتفظ بنسخة احتياطية** من قاعدة البيانات

### **للمستخدم المحدود:**
- ✅ **يمكنك إدارة المخزون** (إضافة/تعديل/حذف منتجات)
- ✅ **يمكنك إجراء المبيعات** وطباعة الفواتير
- ✅ **يمكنك عرض التقارير** الأساسية
- ❌ **لا يمكنك إدارة الفواتير** أو المستخدمين

### **أخطاء شائعة وحلولها:**

#### **"فشل في الاتصال بقاعدة البيانات":**
- تأكد من تشغيل MySQL Server
- تحقق من بيانات الاتصال في الكود

#### **"يوجد منتج بنفس الاسم مسبقاً":**
- غير اسم المنتج أو ابحث عن المنتج الموجود لتعديله

#### **"الكمية المطلوبة أكبر من المتاح":**
- تحقق من المخزون المتاح أو قلل الكمية المطلوبة

#### **"لم يتم العثور على نتائج":**
- تأكد من صحة معايير البحث أو اضغط "عرض الكل"

## 🎯 **سيناريو استخدام كامل:**

### **اليوم الأول - إعداد النظام:**
1. **شغل النظام** → `python main.py`
2. **سجل دخول كمدير** → admin / admin123
3. **أضف منتجات** → إدارة المخزون → إضافة منتجات متنوعة
4. **أنشئ مستخدم للموظف** → إدارة المستخدمين → إضافة مستخدم محدود
5. **اختبر النظام** → اختبار قاعدة البيانات

### **الاستخدام اليومي:**
1. **سجل دخول** (مدير أو مستخدم)
2. **تحقق من المخزون** → إدارة المخزون
3. **أجر المبيعات** → نظام المبيعات
4. **راجع التقارير** → تقرير المبيعات
5. **أضف منتجات جديدة** عند الحاجة

### **نهاية اليوم:**
1. **راجع تقرير المبيعات** لليوم
2. **اطبع تقرير شامل** للفواتير
3. **تحقق من المخزون المنخفض**
4. **سجل خروج** من النظام

---

**🎉 النظام جاهز للاستخدام الكامل!**

**ابدأ الآن:**
1. **شغل النظام** → `python main.py`
2. **سجل دخول** → admin / admin123
3. **أضف منتجك الأول** → إدارة المخزون
4. **أجر أول عملية بيع** → نظام المبيعات
5. **استمتع بالنظام المتكامل!** 🚀
