# 📦 دليل إضافة المنتجات السريع - صيدلية الشفاء

## 🚀 **خطوات إضافة منتج جديد:**

### **1. تسجيل الدخول:**
```
👑 المدير: admin / admin123
```

### **2. فتح نافذة إضافة المنتج:**
1. **اضغط "إدارة المخزون"**
2. **اضغط "+ إضافة منتج جديد"**
3. **ستفتح نافذة بحجم 500x600 بكسل**

### **3. ملء البيانات:**

#### **الحقول المطلوبة:**
- **اسم المنتج:** مثل "باراسيتامول 500mg"
- **الفئة:** مثل "مسكن" (افتراضي: "دواء")
- **سعر الشراء:** مثل "50.00" (افتراضي: "0.00")
- **سعر البيع:** مثل "75.00" (افتراضي: "0.00")
- **الكمية:** مثل "100" (افتراضي: "1")
- **تاريخ الانتهاء:** مثل "2025-12-31" (افتراضي: "2025-12-31")

#### **أمثلة منتجات:**

##### **مثال 1 - دواء:**
```
اسم المنتج: باراسيتامول 500mg
الفئة: مسكن
سعر الشراء: 45.00
سعر البيع: 65.00
الكمية: 200
تاريخ الانتهاء: 2025-08-15
```

##### **مثال 2 - فيتامين:**
```
اسم المنتج: فيتامين سي 1000mg
الفئة: فيتامين
سعر الشراء: 80.00
سعر البيع: 120.00
الكمية: 50
تاريخ الانتهاء: 2026-01-30
```

##### **مثال 3 - مستلزمات طبية:**
```
اسم المنتج: شاش طبي معقم
الفئة: مستلزمات
سعر الشراء: 25.00
سعر البيع: 40.00
الكمية: 300
تاريخ الانتهاء: 2027-06-20
```

### **4. حفظ المنتج:**

#### **طرق الحفظ:**
1. **اضغط زر "✅ إضافة المنتج"**
2. **أو اضغط مفتاح Enter**

#### **طرق الإلغاء:**
1. **اضغط زر "❌ إلغاء"**
2. **أو اضغط مفتاح Escape**
3. **أو اضغط X في أعلى النافذة**

### **5. رسالة النجاح:**
```
✅ تم بنجاح
تم إضافة المنتج 'باراسيتامول 500mg' بنجاح!

الكمية: 200
سعر البيع: 65.00 أوقية
```

## ⚠️ **تحذيرات مهمة:**

### **التحقق من البيانات:**
- ✅ **اسم المنتج مطلوب** ولا يمكن تركه فارغاً
- ✅ **لا يمكن تكرار اسم المنتج** - سيظهر خطأ
- ✅ **الأسعار والكمية** يجب أن تكون أرقام موجبة
- ✅ **تاريخ الانتهاء** اختياري بصيغة YYYY-MM-DD

### **تحذير سعر البيع:**
إذا كان سعر البيع أقل من سعر الشراء:
```
⚠️ تحذير
سعر البيع أقل من سعر الشراء. هل تريد المتابعة؟
[نعم] [لا]
```

### **رسائل الخطأ الشائعة:**

#### **"يرجى ملء جميع الحقول المطلوبة":**
- تأكد من ملء اسم المنتج والفئة والأسعار والكمية

#### **"يرجى إدخال أرقام صحيحة للأسعار والكمية":**
- تأكد من إدخال أرقام فقط في حقول الأسعار والكمية

#### **"لا يمكن أن تكون الأسعار أو الكمية أقل من صفر":**
- أدخل قيم موجبة فقط

#### **"يوجد منتج بنفس الاسم مسبقاً":**
- غير اسم المنتج أو ابحث عن المنتج الموجود لتعديله

#### **"فشل في إضافة المنتج":**
- تحقق من اتصال قاعدة البيانات

## 🎯 **نصائح للاستخدام الأمثل:**

### **تسمية المنتجات:**
- ✅ **استخدم أسماء واضحة ومحددة**
- ✅ **أضف التركيز أو الحجم** مثل "500mg" أو "100ml"
- ✅ **استخدم أسماء فريدة** لكل منتج

### **تصنيف المنتجات:**
- ✅ **استخدم فئات ثابتة** مثل: دواء، فيتامين، مسكن، مضاد حيوي، مستلزمات
- ✅ **كن متسقاً** في التسمية

### **الأسعار:**
- ✅ **احسب هامش ربح معقول** (عادة 20-50%)
- ✅ **راجع أسعار السوق** قبل التسعير
- ✅ **استخدم أرقام عشرية** للدقة

### **الكميات:**
- ✅ **أدخل الكمية الفعلية** المتاحة
- ✅ **حدث الكميات بانتظام** عند وصول شحنات جديدة

### **تواريخ الانتهاء:**
- ✅ **تحقق من التاريخ** على العبوة الأصلية
- ✅ **استخدم صيغة YYYY-MM-DD** مثل 2025-12-31
- ✅ **راقب المنتجات** القريبة من الانتهاء

## 📊 **بعد إضافة المنتج:**

### **التحقق من الإضافة:**
1. **ستعود للمخزون تلقائياً** وسيظهر المنتج الجديد
2. **ابحث عن المنتج** للتأكد من الإضافة
3. **تحقق من البيانات** المعروضة

### **الخطوات التالية:**
1. **أضف منتجات أخرى** حسب الحاجة
2. **جرب عملية بيع** للمنتج الجديد
3. **راجع التقارير** لمتابعة المبيعات

## 🔄 **سيناريو كامل - إضافة 3 منتجات:**

### **المنتج الأول:**
```
1. إدارة المخزون → + إضافة منتج جديد
2. اسم المنتج: أسبرين 100mg
3. الفئة: مسكن
4. سعر الشراء: 30.00
5. سعر البيع: 45.00
6. الكمية: 150
7. تاريخ الانتهاء: 2025-10-15
8. ✅ إضافة المنتج
```

### **المنتج الثاني:**
```
1. + إضافة منتج جديد (مرة أخرى)
2. اسم المنتج: شراب كحة للأطفال
3. الفئة: شراب
4. سعر الشراء: 85.00
5. سعر البيع: 125.00
6. الكمية: 75
7. تاريخ الانتهاء: 2025-07-20
8. ✅ إضافة المنتج
```

### **المنتج الثالث:**
```
1. + إضافة منتج جديد (مرة أخرى)
2. اسم المنتج: قفازات طبية (علبة)
3. الفئة: مستلزمات
4. سعر الشراء: 120.00
5. سعر البيع: 180.00
6. الكمية: 25
7. تاريخ الانتهاء: 2028-01-01
8. ✅ إضافة المنتج
```

### **النتيجة:**
- ✅ **3 منتجات جديدة** في المخزون
- ✅ **جاهزة للبيع** فوراً
- ✅ **تظهر في نظام المبيعات**

---

## 🎉 **الخلاصة:**

### **الخطوات الأساسية:**
1. **تسجيل دخول** → admin / admin123
2. **إدارة المخزون** → + إضافة منتج جديد
3. **ملء البيانات** بدقة
4. **✅ إضافة المنتج**
5. **التحقق من الإضافة** في الجدول

### **المميزات الجديدة:**
- ✅ **أزرار واضحة ومحسنة** (✅ إضافة المنتج / ❌ إلغاء)
- ✅ **نافذة أكبر** (500x600) لسهولة الاستخدام
- ✅ **قيم افتراضية مفيدة** لتسريع الإدخال
- ✅ **رسائل نجاح مفصلة** مع تفاصيل المنتج
- ✅ **اختصارات لوحة المفاتيح** (Enter للحفظ، Escape للإلغاء)
- ✅ **تحديث فوري** للمخزون بعد الإضافة

**🚀 النظام جاهز لإضافة المنتجات بسهولة وسرعة!**
