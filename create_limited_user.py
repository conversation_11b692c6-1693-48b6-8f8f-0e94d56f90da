# -*- coding: utf-8 -*-
"""
إنشاء مستخدم محدود الصلاحيات للاختبار
Create Limited User for Testing
"""

import mysql.connector
import sys

def connect_db():
    """الاتصال بقاعدة البيانات"""
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="pharmacy_db"
        )
    except mysql.connector.Error as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def create_limited_user():
    """إنشاء مستخدم محدود الصلاحيات"""
    db = connect_db()
    if not db:
        return False

    cursor = db.cursor()

    try:
        # التحقق من وجود المستخدم المحدود
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'user'")
        count = cursor.fetchone()[0]

        if count == 0:
            # إنشاء مستخدم محدود
            cursor.execute("""
                INSERT INTO users (username, password, role)
                VALUES ('user', 'user123', 'user')
            """)
            db.commit()
            print("✅ تم إنشاء المستخدم المحدود بنجاح:")
            print("   اسم المستخدم: user")
            print("   كلمة المرور: user123")
            print("   النوع: مستخدم محدود")
            print("   الصلاحيات: لوحة التحكم، المخزون، المبيعات")
        else:
            print("ℹ️  المستخدم المحدود موجود بالفعل")

        # عرض جميع المستخدمين
        print("\n📋 قائمة المستخدمين الحالية:")
        print("-" * 50)
        cursor.execute("SELECT username, role, created_at FROM users ORDER BY created_at")
        users = cursor.fetchall()
        
        for username, role, created_at in users:
            role_name = "مدير النظام" if role == "admin" else "مستخدم محدود"
            created_date = created_at.strftime("%Y-%m-%d") if created_at else "غير محدد"
            print(f"👤 {username:<10} | {role_name:<12} | {created_date}")

        return True

    except mysql.connector.Error as e:
        print(f"❌ خطأ في إنشاء المستخدم: {e}")
        return False
    finally:
        cursor.close()
        db.close()

def show_user_permissions():
    """عرض صلاحيات المستخدمين"""
    print("\n🔐 صلاحيات المستخدمين:")
    print("=" * 60)
    
    print("\n👑 مدير النظام (admin):")
    print("   ✅ لوحة التحكم")
    print("   ✅ إدارة المخزون")
    print("   ✅ نظام المبيعات")
    print("   ✅ إدارة الفواتير")
    print("   ✅ إدارة المستخدمين")
    print("   ✅ التقارير والإحصائيات")
    
    print("\n👤 مستخدم محدود (user):")
    print("   ✅ لوحة التحكم")
    print("   ✅ إدارة المخزون")
    print("   ✅ نظام المبيعات")
    print("   ❌ إدارة الفواتير (للمدير فقط)")
    print("   ❌ إدارة المستخدمين (للمدير فقط)")
    print("   ❌ التقارير المتقدمة (للمدير فقط)")

def test_login_credentials():
    """اختبار بيانات تسجيل الدخول"""
    print("\n🧪 اختبار بيانات تسجيل الدخول:")
    print("=" * 50)
    
    db = connect_db()
    if not db:
        return
    
    cursor = db.cursor()
    
    # اختبار المدير
    cursor.execute("SELECT username, role FROM users WHERE username = 'admin' AND password = 'admin123'")
    admin_result = cursor.fetchone()
    
    if admin_result:
        print("✅ المدير - تسجيل الدخول ناجح")
        print(f"   المستخدم: {admin_result[0]}")
        print(f"   الدور: {admin_result[1]}")
    else:
        print("❌ المدير - فشل في تسجيل الدخول")
    
    # اختبار المستخدم المحدود
    cursor.execute("SELECT username, role FROM users WHERE username = 'user' AND password = 'user123'")
    user_result = cursor.fetchone()
    
    if user_result:
        print("✅ المستخدم المحدود - تسجيل الدخول ناجح")
        print(f"   المستخدم: {user_result[0]}")
        print(f"   الدور: {user_result[1]}")
    else:
        print("❌ المستخدم المحدود - فشل في تسجيل الدخول")
    
    cursor.close()
    db.close()

def main():
    """الدالة الرئيسية"""
    print("🏥 إعداد نظام المستخدمين - صيدلية الشفاء")
    print("=" * 60)
    
    # إنشاء المستخدم المحدود
    if create_limited_user():
        print("\n✅ تم إعداد نظام المستخدمين بنجاح!")
    else:
        print("\n❌ فشل في إعداد نظام المستخدمين")
        sys.exit(1)
    
    # عرض الصلاحيات
    show_user_permissions()
    
    # اختبار تسجيل الدخول
    test_login_credentials()
    
    print("\n" + "=" * 60)
    print("🚀 يمكنك الآن تشغيل النظام:")
    print("   python main.py")
    print("\n📝 بيانات تسجيل الدخول:")
    print("   👑 المدير:")
    print("      اسم المستخدم: admin")
    print("      كلمة المرور: admin123")
    print("   👤 المستخدم المحدود:")
    print("      اسم المستخدم: user")
    print("      كلمة المرور: user123")
    print("\n💡 ملاحظة:")
    print("   - المستخدم المحدود لا يمكنه الوصول لإدارة الفواتير والمستخدمين")
    print("   - سيظهر رسالة خطأ عند محاولة الوصول للصفحات المحظورة")

if __name__ == "__main__":
    main()
