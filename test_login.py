# -*- coding: utf-8 -*-
"""
اختبار شاشة تسجيل الدخول
"""

import tkinter as tk
from tkinter import messagebox

# إعدادات الألوان البسيطة
COLORS = {
    'bg_main': '#f8f9fa',
    'bg_card': '#ffffff', 
    'primary': '#27ae60',
    'text_primary': '#2c3e50',
    'text_secondary': '#34495e',
    'text_white': '#ffffff',
    'btn_success': '#27ae60'
}

FONTS = {
    'title': ('Arial', 18, 'bold'),
    'heading': ('Arial', 14, 'bold'),
    'main': ('Arial', 12),
    'button': ('Arial', 11, 'bold')
}

ICONS = {
    'pharmacy': '🏥',
    'user': '👤'
}

def login():
    """دالة تسجيل الدخول"""
    username = username_entry.get()
    password = password_entry.get()
    
    # إزالة النص التوضيحي
    if username == "اسم المستخدم":
        username = ""
    if password == "كلمة المرور":
        password = ""
    
    if username == "admin" and password == "admin123":
        messagebox.showinfo("مرحباً", f"أهلاً وسهلاً {username}\nتم تسجيل الدخول بنجاح كـ مدير النظام")
        root.destroy()
    elif username == "user" and password == "user123":
        messagebox.showinfo("مرحباً", f"أهلاً وسهلاً {username}\nتم تسجيل الدخول بنجاح كـ مستخدم محدود")
        root.destroy()
    else:
        messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة.")

def create_placeholder_entry(parent, placeholder_text, show_char=None):
    """إنشاء حقل إدخال مع نص توضيحي"""
    entry = tk.Entry(parent, font=FONTS['main'], width=25, relief="solid", bd=1, show=show_char)
    entry.pack(pady=15)
    
    # إضافة placeholder
    entry.insert(0, placeholder_text)
    entry.config(fg='gray')
    
    def on_focus_in(event):
        if entry.get() == placeholder_text:
            entry.delete(0, tk.END)
            entry.config(fg='black')
    
    def on_focus_out(event):
        if entry.get() == '':
            entry.insert(0, placeholder_text)
            entry.config(fg='gray')
    
    entry.bind('<FocusIn>', on_focus_in)
    entry.bind('<FocusOut>', on_focus_out)
    
    return entry

# إنشاء النافذة الرئيسية
root = tk.Tk()
root.title("🏥 صيدلية الشفاء - اختبار تسجيل الدخول")
root.geometry("800x600")
root.configure(bg=COLORS['bg_main'])

# إطار تسجيل الدخول الرئيسي
login_frame = tk.Frame(root, bg=COLORS['bg_main'])
login_frame.pack(fill="both", expand=True)

# إطار تسجيل الدخول المركزي
login_container = tk.Frame(login_frame, bg=COLORS['bg_card'], relief="raised", bd=3)
login_container.place(relx=0.5, rely=0.5, anchor="center", width=400, height=350)

# شعار وعنوان
title_label = tk.Label(login_container, text=f"{ICONS['pharmacy']} صيدلية الشفاء",
                      font=FONTS['title'], bg=COLORS['bg_card'], fg=COLORS['primary'])
title_label.pack(pady=15)

subtitle_label = tk.Label(login_container, text="نظام إدارة شامل ومتطور",
                         font=FONTS['main'], bg=COLORS['bg_card'], fg=COLORS['text_secondary'])
subtitle_label.pack(pady=5)

login_title_label = tk.Label(login_container, text="تسجيل الدخول",
                            font=FONTS['heading'], bg=COLORS['bg_card'], fg=COLORS['text_secondary'])
login_title_label.pack(pady=10)

# حقول الإدخال
username_entry = create_placeholder_entry(login_container, "اسم المستخدم")
password_entry = create_placeholder_entry(login_container, "كلمة المرور", "*")

# ربط Enter بتسجيل الدخول
username_entry.bind("<Return>", lambda event: password_entry.focus())
password_entry.bind("<Return>", lambda event: login())

# زر تسجيل الدخول
login_btn = tk.Button(login_container, text=f"{ICONS['user']} دخول", command=login,
                     bg=COLORS['btn_success'], fg=COLORS['text_white'],
                     font=FONTS['button'], width=20, height=2, relief="flat", cursor="hand2")
login_btn.pack(pady=20)

# معلومات تسجيل الدخول
info_label = tk.Label(login_container, text="المدير: admin / admin123\nالمستخدم: user / user123",
                     font=('Arial', 9), bg=COLORS['bg_card'], fg=COLORS['text_secondary'])
info_label.pack(pady=10)

print("🚀 تم تشغيل اختبار شاشة تسجيل الدخول")
print("📝 بيانات تسجيل الدخول:")
print("   المدير: admin / admin123")
print("   المستخدم: user / user123")

# تشغيل النافذة
root.mainloop()
