# -*- coding: utf-8 -*-
"""
إصلاح مشاكل بدء التشغيل
Startup Issues Fix
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_imports():
    """اختبار استيراد جميع الوحدات المطلوبة"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        from inventory import Inventory_page
        print("✅ inventory.py - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ inventory.py - خطأ في الاستيراد: {e}")
        return False
    
    try:
        from sales import sales_page
        print("✅ sales.py - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ sales.py - خطأ في الاستيراد: {e}")
        return False
    
    try:
        from users import users_page
        print("✅ users.py - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ users.py - خطأ في الاستيراد: {e}")
        return False
    
    try:
        from invoice import invoice_page
        print("✅ invoice.py - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ invoice.py - خطأ في الاستيراد: {e}")
        return False
    
    try:
        from dashboard import DashboardWindowd
        print("✅ dashboard.py - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ dashboard.py - خطأ في الاستيراد: {e}")
        return False
    
    try:
        from theme import COLORS, FONTS, ICONS
        print("✅ theme.py - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ theme.py - خطأ في الاستيراد: {e}")
        return False
    
    return True

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🔍 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        import mysql.connector
        
        db = mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="pharmacy_db"
        )
        
        cursor = db.cursor()
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        
        print(f"✅ الاتصال بقاعدة البيانات ناجح")
        print(f"📊 عدد المستخدمين: {user_count}")
        
        cursor.close()
        db.close()
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_tkinter_setup():
    """اختبار إعداد tkinter"""
    print("\n🔍 اختبار إعداد tkinter...")
    
    try:
        # إنشاء نافذة اختبار
        test_root = tk.Tk()
        test_root.title("اختبار")
        test_root.geometry("300x200")
        
        # إنشاء إطار اختبار
        test_frame = tk.Frame(test_root, bg="white")
        test_frame.pack(fill="both", expand=True)
        
        # إنشاء تسمية اختبار
        test_label = tk.Label(test_frame, text="اختبار tkinter", font=("Arial", 12))
        test_label.pack(pady=20)
        
        # إغلاق النافذة فوراً
        test_root.after(100, test_root.destroy)
        test_root.mainloop()
        
        print("✅ tkinter يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في tkinter: {e}")
        return False

def check_file_structure():
    """التحقق من بنية الملفات"""
    print("\n🔍 التحقق من بنية الملفات...")
    
    required_files = [
        "main.py",
        "inventory.py", 
        "sales.py",
        "users.py",
        "invoice.py",
        "dashboard.py",
        "theme.py",
        "user_management.py",
        "payment_methods.py"
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} - موجود")
        else:
            print(f"❌ {file} - مفقود")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️  الملفات المفقودة: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ جميع الملفات المطلوبة موجودة")
        return True

def run_startup_test():
    """تشغيل اختبار بدء التشغيل"""
    print("\n🚀 تشغيل اختبار بدء التشغيل...")
    
    try:
        # محاولة استيراد main.py
        import main
        print("✅ main.py تم استيراده بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل main.py: {e}")
        return False

def create_minimal_test():
    """إنشاء اختبار مبسط للنظام"""
    print("\n🧪 إنشاء اختبار مبسط...")
    
    try:
        root = tk.Tk()
        root.title("🏥 صيدلية الشفاء - اختبار")
        root.geometry("400x300")
        
        # إطار تسجيل الدخول
        login_frame = tk.Frame(root, bg="#ffffff")
        login_frame.pack(fill="both", expand=True)
        
        # عنوان
        title_label = tk.Label(login_frame, text="🏥 صيدلية الشفاء", 
                              font=("Arial", 16, "bold"), bg="#ffffff", fg="#27ae60")
        title_label.pack(pady=20)
        
        # حقول الإدخال
        username_entry = tk.Entry(login_frame, font=("Arial", 12), width=25)
        username_entry.pack(pady=10)
        username_entry.insert(0, "اسم المستخدم")
        
        password_entry = tk.Entry(login_frame, font=("Arial", 12), width=25, show="*")
        password_entry.pack(pady=10)
        
        # زر تسجيل الدخول
        def test_login():
            messagebox.showinfo("اختبار", "النظام يعمل بشكل صحيح!")
            root.destroy()
        
        login_btn = tk.Button(login_frame, text="👤 دخول", command=test_login,
                             bg="#27ae60", fg="white", font=("Arial", 12), 
                             width=20, height=2)
        login_btn.pack(pady=20)
        
        # تشغيل النافذة
        root.mainloop()
        
        print("✅ الاختبار المبسط نجح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار المبسط: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة تشخيص مشاكل بدء التشغيل - صيدلية الشفاء")
    print("=" * 60)
    
    # التحقق من بنية الملفات
    if not check_file_structure():
        print("\n❌ فشل في التحقق من بنية الملفات")
        return
    
    # اختبار استيراد الوحدات
    if not test_imports():
        print("\n❌ فشل في استيراد الوحدات")
        return
    
    # اختبار قاعدة البيانات
    if not test_database_connection():
        print("\n❌ فشل في الاتصال بقاعدة البيانات")
        print("💡 تأكد من تشغيل MySQL وتشغيل setup.py أو quick_fix.py")
        return
    
    # اختبار tkinter
    if not test_tkinter_setup():
        print("\n❌ فشل في إعداد tkinter")
        return
    
    # اختبار مبسط
    print("\n🧪 تشغيل اختبار مبسط للواجهة...")
    if create_minimal_test():
        print("\n✅ جميع الاختبارات نجحت!")
        print("\n🚀 يمكنك الآن تشغيل النظام:")
        print("   python main.py")
    else:
        print("\n❌ فشل في الاختبار المبسط")
    
    print("\n" + "=" * 60)
    print("🏥 انتهى تشخيص النظام")

if __name__ == "__main__":
    main()
