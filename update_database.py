import mysql.connector
import sys

def connect_db():
    """الاتصال بقاعدة البيانات"""
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="pharmacy_db"
        )
    except mysql.connector.Error as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def create_database_if_not_exists():
    """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
    try:
        # الاتصال بدون تحديد قاعدة بيانات
        connection = mysql.connector.connect(
            host="localhost",
            user="root",
            password=""
        )
        cursor = connection.cursor()

        # إنشاء قاعدة البيانات
        cursor.execute("CREATE DATABASE IF NOT EXISTS pharmacy_db")
        print("تم التأكد من وجود قاعدة البيانات")

        cursor.close()
        connection.close()
        return True

    except mysql.connector.Error as e:
        print(f"خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def create_tables():
    """إنشاء الجداول الأساسية"""
    db = connect_db()
    if not db:
        return False

    cursor = db.cursor()

    try:
        # جدول المستخدمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role VARCHAR(20) DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # جدول المخزون
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS inventory (
                id INT AUTO_INCREMENT PRIMARY KEY,
                product_name VARCHAR(100) UNIQUE NOT NULL,
                category VARCHAR(50) NOT NULL,
                wholesale_price DECIMAL(10,2) NOT NULL,
                selling_price DECIMAL(10,2) NOT NULL,
                quantity INT NOT NULL DEFAULT 0,
                expiry_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        """)

        # جدول المبيعات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sales (
                id INT AUTO_INCREMENT PRIMARY KEY,
                invoice_id VARCHAR(50),
                product_name VARCHAR(100) NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                qty INT NOT NULL,
                total DECIMAL(10,2) NOT NULL,
                time TIME,
                payment_method VARCHAR(20),
                customer VARCHAR(100),
                phone VARCHAR(20),
                date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        db.commit()
        print("تم إنشاء/التأكد من وجود جميع الجداول")

    except mysql.connector.Error as e:
        print(f"خطأ في إنشاء الجداول: {e}")
        return False
    finally:
        cursor.close()
        db.close()

    return True

def insert_default_user():
    """إدراج مستخدم افتراضي"""
    db = connect_db()
    if not db:
        return False

    cursor = db.cursor()

    try:
        # التحقق من وجود مستخدمين
        cursor.execute("SELECT COUNT(*) FROM users")
        count = cursor.fetchone()[0]

        if count == 0:
            # إدراج مستخدم افتراضي
            cursor.execute("""
                INSERT INTO users (username, password, role)
                VALUES ('admin', 'admin123', 'admin')
            """)
            db.commit()
            print("تم إنشاء المستخدم الافتراضي:")
            print("اسم المستخدم: admin")
            print("كلمة المرور: admin123")
        else:
            print("يوجد مستخدمون في النظام")

    except mysql.connector.Error as e:
        print(f"خطأ في إنشاء المستخدم الافتراضي: {e}")
        return False
    finally:
        cursor.close()
        db.close()

    return True

def update_database():
    """تحديث قاعدة البيانات لإضافة عمود invoice_id"""
    db = connect_db()
    if not db:
        return False

    cursor = db.cursor()

    try:
        # التحقق من وجود عمود invoice_id
        cursor.execute("SHOW COLUMNS FROM sales LIKE 'invoice_id'")
        result = cursor.fetchone()

        if not result:
            # إضافة عمود invoice_id
            cursor.execute("ALTER TABLE sales ADD COLUMN invoice_id VARCHAR(50) AFTER id")
            db.commit()
            print("تم إضافة عمود invoice_id بنجاح")
        else:
            print("عمود invoice_id موجود بالفعل")

        return True

    except mysql.connector.Error as e:
        print(f"خطأ في تحديث قاعدة البيانات: {e}")
        return False
    finally:
        cursor.close()
        db.close()

def main():
    """الدالة الرئيسية"""
    print("=== تحديث قاعدة بيانات نظام إدارة الصيدلية ===")
    print()

    # إنشاء قاعدة البيانات
    if not create_database_if_not_exists():
        print("فشل في إنشاء قاعدة البيانات")
        sys.exit(1)

    # إنشاء الجداول
    if not create_tables():
        print("فشل في إنشاء الجداول")
        sys.exit(1)

    # تحديث قاعدة البيانات
    if not update_database():
        print("فشل في تحديث قاعدة البيانات")
        sys.exit(1)

    # إنشاء مستخدم افتراضي
    if not insert_default_user():
        print("فشل في إنشاء المستخدم الافتراضي")
        sys.exit(1)

    print()
    print("=== تم تحديث قاعدة البيانات بنجاح! ===")
    print("يمكنك الآن تشغيل النظام باستخدام: python main.py")

if __name__ == "__main__":
    main()
