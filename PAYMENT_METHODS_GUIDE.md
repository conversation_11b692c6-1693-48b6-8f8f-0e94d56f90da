# 💳 دليل طرق الدفع الموريتانية

## 🎯 **الهدف:**
تحديث نظام طرق الدفع ليشمل المحافظ الإلكترونية الموريتانية الشائعة بدلاً من الطرق التقليدية.

## 💰 **طرق الدفع الجديدة:**

### **1. 💰 النقدي (Cash)**
- **الوصف**: الدفع النقدي المباشر
- **الاستخدام**: للمعاملات المحلية والفورية
- **المزايا**: فوري، بدون رسوم، متاح دائماً

### **2. 📱 بنكيلي (Bankily)**
- **المزود**: Société Générale Mauritanie
- **الوصف**: محفظة بنكيلي الإلكترونية
- **الرمز**: BANKILY
- **الحد الأدنى**: 100 أوقية
- **الحد الأقصى**: 500,000 أوقية
- **التوفر**: 24/7

### **3. 🏦 مصرفي (Masrafi)**
- **المزود**: Banque Nationale de Mauritanie
- **الوصف**: محفظة مصرفي الإلكترونية
- **الرمز**: MASRAFI
- **الحد الأدنى**: 50 أوقية
- **الحد الأقصى**: 300,000 أوقية
- **التوفر**: 24/7

### **4. 💳 السداد (Sedad)**
- **المزود**: Banque Mauritanienne pour le Commerce International
- **الوصف**: محفظة السداد الإلكترونية
- **الرمز**: SEDAD
- **الحد الأدنى**: 100 أوقية
- **الحد الأقصى**: 400,000 أوقية
- **التوفر**: 24/7

### **5. 🏛️ بيك بنك (BIM Bank)**
- **المزود**: Banque Internationale pour la Mauritanie
- **الوصف**: محفظة بيك بنك الإلكترونية
- **الرمز**: BIM
- **الحد الأدنى**: 100 أوقية
- **الحد الأقصى**: 600,000 أوقية
- **التوفر**: 24/7

### **6. 🖱️ كليك (Click)**
- **المزود**: Attijari Bank Mauritanie
- **الوصف**: محفظة كليك الإلكترونية
- **الرمز**: CLICK
- **الحد الأدنى**: 50 أوقية
- **الحد الأقصى**: 250,000 أوقية
- **التوفر**: 24/7

## 🔧 **التحديثات التقنية:**

### **1. ملف payment_methods.py الجديد:**
```python
# طرق الدفع المتاحة
PAYMENT_METHODS = {
    'cash': {
        'name_ar': 'نقدي',
        'name_en': 'Cash',
        'icon': '💰',
        'display': '💰 نقدي',
        'type': 'cash'
    },
    'bankily': {
        'name_ar': 'بنكيلي',
        'name_en': 'Bankily',
        'icon': '📱',
        'display': '📱 بنكيلي (Bankily)',
        'type': 'mobile_wallet'
    },
    # ... باقي المحافظ
}
```

### **2. دوال مساعدة:**
```python
def get_payment_methods_list():
    """الحصول على قائمة طرق الدفع للعرض"""
    
def clean_payment_method(method_display):
    """تنظيف طريقة الدفع للحفظ في قاعدة البيانات"""
    
def validate_payment_amount(method_display, amount):
    """التحقق من صحة المبلغ لطريقة الدفع"""
```

### **3. تحديث واجهة المبيعات:**
```python
# في sales.py
from payment_methods import get_payment_methods_list, clean_payment_method

# استخدام القائمة الجديدة
payment_methods = get_payment_methods_list()
method_combo = ttk.Combobox(form_frame, values=payment_methods,
                           width=20, font=("Arial", 10), state="readonly")
```

## 📱 **الواجهة المحدثة:**

### **قبل التحديث:**
```
طريقة الدفع: [نقدي ▼]
الخيارات:
- نقدي
- بطاقة ائتمان
- تحويل بنكي
```

### **بعد التحديث:**
```
طريقة الدفع: [💰 نقدي ▼]
الخيارات:
- 💰 نقدي
- 📱 بنكيلي (Bankily)
- 🏦 مصرفي (Masrafi)
- 💳 السداد (Sedad)
- 🏛️ بيك بنك (BIM Bank)
- 🖱️ كليك (Click)
```

## 🗄️ **قاعدة البيانات:**

### **الحفظ في قاعدة البيانات:**
```sql
-- يتم حفظ الأسماء المنظفة بدون أيقونات
INSERT INTO sales (payment_method, ...) VALUES ('نقدي', ...)
INSERT INTO sales (payment_method, ...) VALUES ('بنكيلي', ...)
INSERT INTO sales (payment_method, ...) VALUES ('مصرفي', ...)
```

### **العرض في الواجهة:**
```
💰 نقدي
📱 بنكيلي (Bankily)
🏦 مصرفي (Masrafi)
💳 السداد (Sedad)
🏛️ بيك بنك (BIM Bank)
🖱️ كليك (Click)
```

## 🧾 **الفواتير المحدثة:**

### **في الفاتورة HTML:**
```html
<div class="info-item">
    <span class="info-label">طريقة الدفع:</span>
    <span class="info-value">بنكيلي</span>
</div>
```

### **في الفاتورة النصية:**
```
طريقة الدفع: بنكيلي
```

## 🔍 **مثال على الاستخدام:**

### **سيناريو البيع:**
```
1. العميل يختار منتجات بقيمة 1,500 أوقية
2. الصيدلي يختار "📱 بنكيلي (Bankily)" كطريقة دفع
3. النظام يتحقق من الحدود (100 - 500,000 أوقية) ✅
4. يتم حفظ "بنكيلي" في قاعدة البيانات
5. تظهر في الفاتورة: "طريقة الدفع: بنكيلي"
```

### **التحقق من المبلغ:**
```python
# مثال على التحقق
method = "📱 بنكيلي (Bankily)"
amount = 1500

is_valid, message = validate_payment_amount(method, amount)
if is_valid:
    print("✅ المبلغ صحيح")
else:
    print(f"❌ {message}")
```

## 📊 **إحصائيات طرق الدفع:**

### **تقرير المبيعات حسب طريقة الدفع:**
```sql
SELECT payment_method, COUNT(*) as count, SUM(total) as total_amount
FROM sales 
GROUP BY payment_method 
ORDER BY total_amount DESC;
```

### **النتائج المتوقعة:**
```
طريقة الدفع    | عدد المعاملات | الإجمالي
نقدي          | 45            | 15,000 أوقية
بنكيلي        | 23            | 8,500 أوقية
مصرفي         | 18            | 6,200 أوقية
السداد        | 12            | 4,100 أوقية
بيك بنك       | 8             | 2,800 أوقية
كليك          | 5             | 1,900 أوقية
```

## 🛡️ **الأمان والتحقق:**

### **التحقق من صحة البيانات:**
```python
def validate_payment_data(method, amount, phone=None):
    """التحقق الشامل من بيانات الدفع"""
    
    # التحقق من المبلغ
    is_valid, message = validate_payment_amount(method, amount)
    if not is_valid:
        return False, message
    
    # التحقق من رقم الهاتف للمحافظ الإلكترونية
    if is_mobile_wallet(method) and not phone:
        return False, "رقم الهاتف مطلوب للمحافظ الإلكترونية"
    
    return True, ""
```

### **معالجة الأخطاء:**
```python
try:
    # معالجة الدفع
    process_payment(method, amount)
except PaymentError as e:
    show_error_message(f"خطأ في الدفع: {e}")
except Exception as e:
    show_error_message(f"خطأ غير متوقع: {e}")
```

## 🌐 **التوطين والترجمة:**

### **الأسماء العربية:**
- نقدي
- بنكيلي
- مصرفي
- السداد
- بيك بنك
- كليك

### **الأسماء الإنجليزية:**
- Cash
- Bankily
- Masrafi
- Sedad
- BIM Bank
- Click

## 📱 **التوافق مع الأجهزة:**

### **الهواتف المدعومة:**
- جميع الهواتف الذكية
- الهواتف العادية (للرسائل النصية)
- أجهزة نقاط البيع

### **أنظمة التشغيل:**
- Android
- iOS
- Windows Mobile
- Feature Phones

## 💡 **نصائح للاستخدام:**

### **للصيدلي:**
1. **تأكد من رقم الهاتف** عند استخدام المحافظ الإلكترونية
2. **راجع حدود المبالغ** لكل محفظة
3. **احتفظ بإيصالات التأكيد** للمعاملات الإلكترونية
4. **تحقق من رصيد العميل** قبل إتمام المعاملة

### **للعميل:**
1. **تأكد من رصيد المحفظة** قبل الشراء
2. **احتفظ برقم المعاملة** للمراجعة
3. **تحقق من تفاصيل الفاتورة** قبل الدفع
4. **أبلغ عن أي مشاكل** فوراً

## 🚀 **المزايا الجديدة:**

### **للصيدلية:**
- ✅ **تنوع طرق الدفع** يجذب المزيد من العملاء
- ✅ **تقليل التعامل النقدي** وزيادة الأمان
- ✅ **تتبع أفضل للمعاملات** إلكترونياً
- ✅ **سرعة في المعاملات** وتقليل الأخطاء

### **للعملاء:**
- ✅ **سهولة الدفع** بدون حمل نقود
- ✅ **أمان أكبر** في المعاملات
- ✅ **سجل إلكتروني** للمشتريات
- ✅ **سرعة في الإنجاز** وتوفير الوقت

### **للنظام:**
- ✅ **تنظيم أفضل** لطرق الدفع
- ✅ **سهولة الصيانة** والتطوير
- ✅ **مرونة في الإضافة** لطرق جديدة
- ✅ **تقارير دقيقة** حسب طريقة الدفع

---

**💳 نظام دفع متطور يواكب التطور التقني في موريتانيا!**

**💡 تذكر**: الآن يمكن للعملاء الدفع بجميع المحافظ الإلكترونية الشائعة في موريتانيا بسهولة وأمان!
