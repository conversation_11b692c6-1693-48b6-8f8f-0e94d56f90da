# -*- coding: utf-8 -*-
"""
ملف الهوية البصرية والعلامة التجارية للصيدلية
Brand Identity and Visual Elements for the Pharmacy
"""

# ==== معلومات الصيدلية الأساسية ====
PHARMACY_INFO = {
    # الأسماء
    'name_ar': 'صيدلية الشفاء',
    'name_en': 'Al-Shifa Pharmacy',
    'slogan_ar': 'نظام إدارة شامل ومتطور',
    'slogan_en': 'Comprehensive Management System',
    
    # معلومات الاتصال
    'phone': '+966 12 345 6789',
    'email': '<EMAIL>',
    'address_ar': 'شارع الملك فهد، الرياض، المملكة العربية السعودية',
    'address_en': 'King Fahd Street, Riyadh, Saudi Arabia',
    'website': 'www.alshifa-pharmacy.com',
    
    # معلومات الترخيص
    'license_no': 'PH-2024-001',
    'tax_no': '123456789012345',
    'cr_no': '**********',
}

# ==== الشعارات والأيقونات ====
LOGOS = {
    # الشعار الرئيسي
    'main': '🏥',
    'main_large': '🏥',
    
    # شعارات فرعية
    'medical': '⚕️',
    'pills': '💊',
    'health': '🩺',
    'cross': '✚',
    'heart': '❤️',
    
    # أيقونات النظام
    'system': '⚙️',
    'management': '📊',
    'database': '🗄️',
}

# ==== ألوان الهوية البصرية ====
BRAND_COLORS = {
    # الألوان الأساسية
    'primary': '#27ae60',      # أخضر طبي
    'secondary': '#2ecc71',    # أخضر فاتح
    'accent': '#3498db',       # أزرق
    
    # ألوان مساعدة
    'success': '#27ae60',      # أخضر نجاح
    'warning': '#f39c12',      # برتقالي تحذير
    'error': '#e74c3c',        # أحمر خطأ
    'info': '#3498db',         # أزرق معلومات
    
    # ألوان النصوص
    'text_primary': '#2c3e50',
    'text_secondary': '#7f8c8d',
    'text_white': '#ffffff',
    'text_light': '#ecf0f1',
    
    # ألوان الخلفيات
    'bg_main': '#ffffff',
    'bg_secondary': '#f8f9fa',
    'bg_card': '#ffffff',
    'bg_sidebar': '#2c3e50',
    'bg_header': '#27ae60',
}

# ==== خطوط النظام ====
BRAND_FONTS = {
    # الخطوط العربية
    'arabic_main': ('Cairo', 12),
    'arabic_title': ('Cairo', 18, 'bold'),
    'arabic_heading': ('Cairo', 14, 'bold'),
    'arabic_small': ('Cairo', 10),
    
    # الخطوط الإنجليزية
    'english_main': ('Arial', 12),
    'english_title': ('Arial', 18, 'bold'),
    'english_heading': ('Arial', 14, 'bold'),
    'english_small': ('Arial', 10),
    
    # خطوط النظام
    'system': ('Helvetica', 11),
    'button': ('Arial', 11, 'bold'),
    'label': ('Arial', 10),
}

# ==== قوالب النصوص ====
TEXT_TEMPLATES = {
    # عناوين النوافذ
    'window_titles': {
        'main': '🏥 صيدلية الشفاء - نظام إدارة شامل',
        'dashboard': '📊 صيدلية الشفاء - لوحة التحكم',
        'inventory': '📦 صيدلية الشفاء - إدارة المخزون',
        'sales': '🛒 صيدلية الشفاء - نظام المبيعات',
        'invoices': '🧾 صيدلية الشفاء - إدارة الفواتير',
        'users': '👥 صيدلية الشفاء - إدارة المستخدمين',
    },
    
    # رؤوس الصفحات
    'page_headers': {
        'dashboard': '🏥 صيدلية الشفاء - لوحة التحكم الرئيسية',
        'inventory': '📦 إدارة مخزون صيدلية الشفاء',
        'sales': '🛒 نظام مبيعات صيدلية الشفاء',
        'invoices': '🧾 فواتير صيدلية الشفاء',
        'users': '👥 مستخدمو صيدلية الشفاء',
    },
    
    # رسائل الترحيب
    'welcome_messages': {
        'login': 'مرحباً بك في صيدلية الشفاء',
        'dashboard': 'أهلاً وسهلاً في نظام إدارة صيدلية الشفاء',
        'success': 'تم بنجاح في صيدلية الشفاء',
    },
    
    # رسائل الشكر
    'thank_you_messages': {
        'invoice': 'شكراً لتعاملكم مع صيدلية الشفاء',
        'visit': 'نتمنى لكم دوام الصحة والعافية',
        'service': 'صيدلية الشفاء في خدمتكم دائماً',
    }
}

# ==== قوالب الفواتير ====
INVOICE_TEMPLATES = {
    # رأس الفاتورة
    'header': {
        'title_ar': '🏥 صيدلية الشفاء',
        'title_en': 'Al-Shifa Pharmacy',
        'subtitle_ar': 'نظام إدارة شامل ومتطور',
        'subtitle_en': 'Comprehensive Management System',
    },
    
    # معلومات الصيدلية في الفاتورة
    'pharmacy_info': {
        'name': 'صيدلية الشفاء - Al-Shifa Pharmacy',
        'address': 'شارع الملك فهد، الرياض',
        'phone': 'هاتف: +966 12 345 6789',
        'email': 'البريد: <EMAIL>',
        'license': 'ترخيص: PH-2024-001',
    },
    
    # خاتمة الفاتورة
    'footer': {
        'thanks_ar': '🙏 شكراً لتعاملكم مع صيدلية الشفاء',
        'thanks_en': 'Thank you for choosing Al-Shifa Pharmacy',
        'wishes_ar': 'نتمنى لكم دوام الصحة والعافية',
        'wishes_en': 'Wishing you good health and wellness',
        'service_ar': 'صيدلية الشفاء في خدمتكم دائماً',
        'service_en': 'Al-Shifa Pharmacy is always at your service',
    }
}

# ==== إعدادات التصميم ====
DESIGN_SETTINGS = {
    # أحجام العناصر
    'button_width': 15,
    'button_height': 2,
    'entry_width': 20,
    'frame_padding': 10,
    
    # المسافات
    'spacing_small': 5,
    'spacing_medium': 10,
    'spacing_large': 20,
    'spacing_xlarge': 30,
    
    # الحدود والظلال
    'border_radius': 5,
    'shadow_offset': 2,
    'border_width': 1,
    
    # الشفافية
    'opacity_hover': 0.8,
    'opacity_disabled': 0.5,
    'opacity_overlay': 0.9,
}

# ==== دوال مساعدة ====
def get_pharmacy_name(language='ar'):
    """الحصول على اسم الصيدلية باللغة المحددة"""
    if language == 'en':
        return PHARMACY_INFO['name_en']
    return PHARMACY_INFO['name_ar']

def get_full_title(page='main', language='ar'):
    """الحصول على العنوان الكامل للصفحة"""
    if page in TEXT_TEMPLATES['window_titles']:
        return TEXT_TEMPLATES['window_titles'][page]
    return TEXT_TEMPLATES['window_titles']['main']

def get_brand_color(color_name):
    """الحصول على لون من ألوان الهوية البصرية"""
    return BRAND_COLORS.get(color_name, BRAND_COLORS['primary'])

def get_logo(size='main'):
    """الحصول على الشعار بالحجم المحدد"""
    return LOGOS.get(size, LOGOS['main'])

def format_invoice_header():
    """تنسيق رأس الفاتورة"""
    header = INVOICE_TEMPLATES['header']
    return f"{header['title_ar']}\n{header['title_en']}\n{header['subtitle_ar']}"

def format_invoice_footer():
    """تنسيق خاتمة الفاتورة"""
    footer = INVOICE_TEMPLATES['footer']
    return f"{footer['thanks_ar']}\n{footer['wishes_ar']}\n{footer['service_ar']}"

# ==== تصدير المتغيرات الرئيسية ====
__all__ = [
    'PHARMACY_INFO',
    'LOGOS', 
    'BRAND_COLORS',
    'BRAND_FONTS',
    'TEXT_TEMPLATES',
    'INVOICE_TEMPLATES',
    'DESIGN_SETTINGS',
    'get_pharmacy_name',
    'get_full_title',
    'get_brand_color',
    'get_logo',
    'format_invoice_header',
    'format_invoice_footer'
]
