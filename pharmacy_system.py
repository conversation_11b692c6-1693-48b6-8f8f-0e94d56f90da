# -*- coding: utf-8 -*-
"""
نظام صيدلية الشفاء - نسخة محسنة ومضمونة
"""

import tkinter as tk
from tkinter import messagebox, ttk
import mysql.connector
import sys

# إعدادات النظام
SYSTEM_CONFIG = {
    'title': '🏥 صيدلية الشفاء - نظام إدارة شامل',
    'geometry': '1200x700',
    'bg_color': '#f8f9fa'
}

# الألوان
COLORS = {
    'primary': '#27ae60',
    'secondary': '#2c3e50', 
    'accent': '#3498db',
    'success': '#27ae60',
    'error': '#e74c3c',
    'warning': '#f39c12',
    'bg_main': '#f8f9fa',
    'bg_card': '#ffffff',
    'bg_sidebar': '#2c3e50',
    'text_white': '#ffffff',
    'text_dark': '#2c3e50'
}

# الخطوط
FONTS = {
    'title': ('Arial', 18, 'bold'),
    'heading': ('Arial', 14, 'bold'),
    'normal': ('Arial', 12),
    'small': ('Arial', 10)
}

class PharmacySystem:
    def __init__(self):
        self.current_user = {'username': '', 'role': ''}
        self.setup_main_window()
        self.create_login_screen()
        
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root = tk.Tk()
        self.root.title(SYSTEM_CONFIG['title'])
        self.root.geometry(SYSTEM_CONFIG['geometry'])
        self.root.configure(bg=SYSTEM_CONFIG['bg_color'])
        
        # جعل النافذة في المقدمة
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(self.root.attributes, '-topmost', False)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_login_screen(self):
        """إنشاء شاشة تسجيل الدخول"""
        # إطار تسجيل الدخول الرئيسي
        self.login_frame = tk.Frame(self.root, bg=COLORS['bg_main'])
        self.login_frame.pack(fill="both", expand=True)
        
        # إطار مركزي لتسجيل الدخول
        login_container = tk.Frame(self.login_frame, bg=COLORS['bg_card'], 
                                  relief="raised", bd=2, padx=30, pady=30)
        login_container.place(relx=0.5, rely=0.5, anchor="center")
        
        # العنوان والشعار
        title_label = tk.Label(login_container, text="🏥 صيدلية الشفاء",
                              font=FONTS['title'], bg=COLORS['bg_card'], 
                              fg=COLORS['primary'])
        title_label.pack(pady=(0, 10))
        
        subtitle_label = tk.Label(login_container, text="نظام إدارة شامل ومتطور",
                                 font=FONTS['normal'], bg=COLORS['bg_card'], 
                                 fg=COLORS['text_dark'])
        subtitle_label.pack(pady=(0, 20))
        
        login_title = tk.Label(login_container, text="تسجيل الدخول",
                              font=FONTS['heading'], bg=COLORS['bg_card'], 
                              fg=COLORS['secondary'])
        login_title.pack(pady=(0, 20))
        
        # حقول الإدخال
        tk.Label(login_container, text="اسم المستخدم:", font=FONTS['normal'],
                bg=COLORS['bg_card'], fg=COLORS['text_dark']).pack(anchor="w", pady=(0, 5))
        
        self.username_entry = tk.Entry(login_container, font=FONTS['normal'], 
                                      width=30, relief="solid", bd=1)
        self.username_entry.pack(pady=(0, 15))
        
        tk.Label(login_container, text="كلمة المرور:", font=FONTS['normal'],
                bg=COLORS['bg_card'], fg=COLORS['text_dark']).pack(anchor="w", pady=(0, 5))
        
        self.password_entry = tk.Entry(login_container, font=FONTS['normal'], 
                                      width=30, relief="solid", bd=1, show="*")
        self.password_entry.pack(pady=(0, 20))
        
        # زر تسجيل الدخول
        login_btn = tk.Button(login_container, text="👤 دخول", 
                             command=self.login, font=FONTS['heading'],
                             bg=COLORS['success'], fg=COLORS['text_white'],
                             width=25, height=2, relief="flat", cursor="hand2")
        login_btn.pack(pady=(0, 20))
        
        # معلومات تسجيل الدخول
        info_frame = tk.Frame(login_container, bg=COLORS['bg_card'])
        info_frame.pack()
        
        tk.Label(info_frame, text="بيانات تسجيل الدخول:", font=FONTS['small'],
                bg=COLORS['bg_card'], fg=COLORS['text_dark']).pack()
        tk.Label(info_frame, text="المدير: admin / admin123", font=FONTS['small'],
                bg=COLORS['bg_card'], fg=COLORS['text_dark']).pack()
        tk.Label(info_frame, text="المستخدم: user / user123", font=FONTS['small'],
                bg=COLORS['bg_card'], fg=COLORS['text_dark']).pack()
        
        # ربط Enter للانتقال السريع
        self.username_entry.bind("<Return>", lambda e: self.password_entry.focus())
        self.password_entry.bind("<Return>", lambda e: self.login())
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
        
    def connect_db(self):
        """الاتصال بقاعدة البيانات"""
        try:
            return mysql.connector.connect(
                host="localhost",
                user="root",
                password="",
                database="pharmacy_db"
            )
        except mysql.connector.Error as e:
            messagebox.showerror("خطأ قاعدة البيانات", f"فشل في الاتصال بقاعدة البيانات:\n{str(e)}")
            return None
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع:\n{str(e)}")
            return None
            
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        # التحقق من صحة البيانات
        if not username:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return
            
        if not password:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return
        
        # الاتصال بقاعدة البيانات والتحقق
        db = self.connect_db()
        if not db:
            return
            
        try:
            cursor = db.cursor()
            query = "SELECT username, role FROM users WHERE username = %s AND password = %s"
            cursor.execute(query, (username, password))
            result = cursor.fetchone()
            
            if result:
                # تسجيل دخول ناجح
                self.current_user['username'] = result[0]
                self.current_user['role'] = result[1]
                
                # رسالة ترحيب
                role_name = "مدير النظام" if result[1] == 'admin' else "مستخدم محدود"
                messagebox.showinfo("مرحباً", 
                                   f"أهلاً وسهلاً {result[0]}\n"
                                   f"تم تسجيل الدخول بنجاح كـ {role_name}")
                
                # الانتقال للنظام الرئيسي
                self.show_main_system()
            else:
                # بيانات خاطئة
                messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
                self.password_entry.delete(0, tk.END)
                self.password_entry.focus()
                
        except mysql.connector.Error as e:
            messagebox.showerror("خطأ قاعدة البيانات", f"حدث خطأ في قاعدة البيانات:\n{str(e)}")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع:\n{str(e)}")
        finally:
            if db:
                cursor.close()
                db.close()
                
    def show_main_system(self):
        """عرض النظام الرئيسي"""
        # إخفاء شاشة تسجيل الدخول
        self.login_frame.destroy()
        
        # إنشاء الشريط العلوي
        self.create_top_bar()
        
        # إنشاء القائمة الجانبية
        self.create_sidebar()
        
        # إنشاء المحتوى الرئيسي
        self.create_main_content()
        
        # عرض لوحة التحكم
        self.show_dashboard()
        
    def create_top_bar(self):
        """إنشاء الشريط العلوي"""
        self.top_bar = tk.Frame(self.root, bg=COLORS['primary'], height=50)
        self.top_bar.pack(side="top", fill="x")
        self.top_bar.pack_propagate(False)
        
        # عنوان النظام
        title_label = tk.Label(self.top_bar, text="🏥 صيدلية الشفاء - نظام إدارة شامل",
                              font=FONTS['heading'], bg=COLORS['primary'], 
                              fg=COLORS['text_white'])
        title_label.pack(side="left", padx=20, pady=10)
        
        # معلومات المستخدم
        username = self.current_user['username']
        role = self.current_user['role']
        role_name = "مدير النظام" if role == 'admin' else "مستخدم محدود"
        
        self.user_info_label = tk.Label(self.top_bar, text=f"👤 {username} ({role_name})",
                                       font=FONTS['normal'], bg=COLORS['primary'], 
                                       fg=COLORS['text_white'])
        self.user_info_label.pack(side="right", padx=20, pady=10)
        
    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        self.sidebar = tk.Frame(self.root, bg=COLORS['bg_sidebar'], width=250)
        self.sidebar.pack(side="left", fill="y")
        self.sidebar.pack_propagate(False)
        
        # شعار في القائمة
        logo_frame = tk.Frame(self.sidebar, bg=COLORS['bg_sidebar'])
        logo_frame.pack(fill="x", pady=20)
        
        tk.Label(logo_frame, text="🏥", font=('Arial', 30),
                bg=COLORS['bg_sidebar'], fg=COLORS['text_white']).pack()
        tk.Label(logo_frame, text="صيدلية الشفاء", font=FONTS['heading'],
                bg=COLORS['bg_sidebar'], fg=COLORS['text_white']).pack()
        
        # أزرار القائمة
        self.create_menu_buttons()
        
    def create_menu_buttons(self):
        """إنشاء أزرار القائمة"""
        # أزرار أساسية لجميع المستخدمين
        basic_buttons = [
            ("📊 لوحة التحكم", self.show_dashboard),
            ("📦 المخزون", self.show_inventory),
            ("🛒 المبيعات", self.show_sales),
        ]
        
        # أزرار المدير فقط
        admin_buttons = [
            ("🧾 الفواتير", self.show_invoices),
            ("👥 المستخدمين", self.show_users),
        ]
        
        # تجميع الأزرار
        all_buttons = basic_buttons.copy()
        if self.current_user.get('role') == 'admin':
            all_buttons.extend(admin_buttons)
        all_buttons.append(("🚪 تسجيل خروج", self.logout))
        
        # إنشاء الأزرار
        for text, command in all_buttons:
            btn = tk.Button(self.sidebar, text=text, command=command,
                           bg=COLORS['bg_sidebar'], fg=COLORS['text_white'],
                           font=FONTS['normal'], relief="flat", anchor="w",
                           padx=20, pady=12, width=25)
            btn.pack(fill="x", pady=2, padx=10)
            
            # تأثير hover
            self.add_hover_effect(btn)
            
    def add_hover_effect(self, button):
        """إضافة تأثير hover للأزرار"""
        def on_enter(event):
            button.configure(bg=COLORS['accent'])
            
        def on_leave(event):
            button.configure(bg=COLORS['bg_sidebar'])
            
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        
    def create_main_content(self):
        """إنشاء منطقة المحتوى الرئيسي"""
        self.main_content = tk.Frame(self.root, bg=COLORS['bg_main'])
        self.main_content.pack(side="right", fill="both", expand=True)
        
    def clear_main_content(self):
        """مسح المحتوى الرئيسي"""
        for widget in self.main_content.winfo_children():
            widget.destroy()
            
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.clear_main_content()
        
        # عنوان الصفحة
        title_label = tk.Label(self.main_content, text="📊 لوحة التحكم",
                              font=FONTS['title'], bg=COLORS['bg_main'], 
                              fg=COLORS['primary'])
        title_label.pack(pady=30)
        
        # رسالة ترحيب
        welcome_label = tk.Label(self.main_content, 
                                text=f"مرحباً بك {self.current_user['username']}",
                                font=FONTS['heading'], bg=COLORS['bg_main'], 
                                fg=COLORS['text_dark'])
        welcome_label.pack(pady=20)
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(self.main_content, bg=COLORS['bg_main'])
        stats_frame.pack(pady=30)
        
        # عرض إحصائيات بسيطة
        self.show_statistics(stats_frame)
        
    def show_statistics(self, parent):
        """عرض الإحصائيات"""
        try:
            db = self.connect_db()
            if db:
                cursor = db.cursor()
                
                # إحصائيات المنتجات
                cursor.execute("SELECT COUNT(*) FROM products")
                products_count = cursor.fetchone()[0] if cursor.rowcount > 0 else 0
                
                # إحصائيات المبيعات
                cursor.execute("SELECT COUNT(*) FROM sales")
                sales_count = cursor.fetchone()[0] if cursor.rowcount > 0 else 0
                
                # إحصائيات المستخدمين
                cursor.execute("SELECT COUNT(*) FROM users")
                users_count = cursor.fetchone()[0] if cursor.rowcount > 0 else 0
                
                cursor.close()
                db.close()
                
                # عرض الإحصائيات
                stats = [
                    ("📦 المنتجات", products_count),
                    ("🛒 المبيعات", sales_count),
                    ("👥 المستخدمين", users_count)
                ]
                
                for icon_text, count in stats:
                    stat_label = tk.Label(parent, text=f"{icon_text}: {count}",
                                         font=FONTS['normal'], bg=COLORS['bg_main'],
                                         fg=COLORS['text_dark'])
                    stat_label.pack(pady=5)
                    
        except Exception as e:
            error_label = tk.Label(parent, text="النظام يعمل بشكل صحيح!",
                                  font=FONTS['normal'], bg=COLORS['bg_main'],
                                  fg=COLORS['text_dark'])
            error_label.pack(pady=10)
            
    def show_inventory(self):
        """عرض صفحة المخزون"""
        self.clear_main_content()
        tk.Label(self.main_content, text="📦 إدارة المخزون",
                font=FONTS['title'], bg=COLORS['bg_main'], 
                fg=COLORS['primary']).pack(pady=50)
        tk.Label(self.main_content, text="صفحة المخزون قيد التطوير",
                font=FONTS['normal'], bg=COLORS['bg_main']).pack()
                
    def show_sales(self):
        """عرض صفحة المبيعات"""
        self.clear_main_content()
        tk.Label(self.main_content, text="🛒 إدارة المبيعات",
                font=FONTS['title'], bg=COLORS['bg_main'], 
                fg=COLORS['primary']).pack(pady=50)
        tk.Label(self.main_content, text="صفحة المبيعات قيد التطوير",
                font=FONTS['normal'], bg=COLORS['bg_main']).pack()
                
    def show_invoices(self):
        """عرض صفحة الفواتير"""
        self.clear_main_content()
        tk.Label(self.main_content, text="🧾 إدارة الفواتير",
                font=FONTS['title'], bg=COLORS['bg_main'], 
                fg=COLORS['primary']).pack(pady=50)
        tk.Label(self.main_content, text="صفحة الفواتير قيد التطوير",
                font=FONTS['normal'], bg=COLORS['bg_main']).pack()
                
    def show_users(self):
        """عرض صفحة المستخدمين"""
        self.clear_main_content()
        tk.Label(self.main_content, text="👥 إدارة المستخدمين",
                font=FONTS['title'], bg=COLORS['bg_main'], 
                fg=COLORS['primary']).pack(pady=50)
        tk.Label(self.main_content, text="صفحة المستخدمين قيد التطوير",
                font=FONTS['normal'], bg=COLORS['bg_main']).pack()
                
    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل خروج", "هل تريد تسجيل الخروج من النظام؟")
        if result:
            # مسح بيانات المستخدم
            self.current_user = {'username': '', 'role': ''}
            
            # إغلاق النافذة الحالية
            self.root.destroy()
            
            # إنشاء نظام جديد
            new_system = PharmacySystem()
            new_system.run()
            
    def run(self):
        """تشغيل النظام"""
        print("🚀 تم تشغيل نظام صيدلية الشفاء")
        print("📝 بيانات تسجيل الدخول:")
        print("   👑 المدير: admin / admin123")
        print("   👤 المستخدم: user / user123")
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
        except Exception as e:
            print(f"❌ حدث خطأ: {str(e)}")

if __name__ == "__main__":
    try:
        system = PharmacySystem()
        system.run()
    except Exception as e:
        print(f"❌ فشل في تشغيل النظام: {str(e)}")
        input("اضغط Enter للخروج...")
