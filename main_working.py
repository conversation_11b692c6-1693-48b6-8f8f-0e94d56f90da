# -*- coding: utf-8 -*-
"""
نظام صيدلية الشفاء - نسخة عاملة مضمونة
"""

import tkinter as tk
from tkinter import messagebox, ttk
import mysql.connector

# إعدادات الألوان والخطوط
COLORS = {
    'primary': '#27ae60',
    'bg_main': '#f8f9fa',
    'bg_card': '#ffffff',
    'bg_sidebar': '#2c3e50',
    'text_primary': '#2c3e50',
    'text_secondary': '#34495e',
    'text_white': '#ffffff',
    'btn_success': '#27ae60',
    'accent': '#3498db',
    'error': '#e74c3c'
}

FONTS = {
    'title': ('Arial', 18, 'bold'),
    'heading': ('Arial', 14, 'bold'),
    'main': ('Arial', 12),
    'button': ('Arial', 11, 'bold')
}

ICONS = {
    'pharmacy': '🏥',
    'user': '👤',
    'dashboard': '📊',
    'inventory': '📦',
    'sales': '🛒',
    'invoice': '🧾',
    'users': '👥',
    'logout': '🚪'
}

# ==== الاتصال بقاعدة البيانات ====
def connect_db():
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="pharmacy_db"
        )
    except:
        return None

# ==== متغير المستخدم الحالي ====
current_user = {'username': '', 'role': ''}

# ==== دالة تسجيل الدخول ====
def login():
    username = username_entry.get().strip()
    password = password_entry.get().strip()
    
    # إزالة النص التوضيحي
    if username == "اسم المستخدم" or username == "":
        messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم.")
        return
    if password == "كلمة المرور" or password == "":
        messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور.")
        return
    
    try:
        db = connect_db()
        if not db:
            messagebox.showerror("خطأ", "فشل في الاتصال بقاعدة البيانات.")
            return
            
        cursor = db.cursor()
        cursor.execute("SELECT username, role FROM users WHERE username = %s AND password = %s", 
                      (username, password))
        result = cursor.fetchone()
        cursor.close()
        db.close()
        
        if result:
            # حفظ معلومات المستخدم الحالي
            current_user['username'] = result[0]
            current_user['role'] = result[1]
            
            # رسالة ترحيب
            role_name = "مدير النظام" if result[1] == 'admin' else "مستخدم محدود"
            messagebox.showinfo("مرحباً", f"أهلاً وسهلاً {result[0]}\nتم تسجيل الدخول بنجاح كـ {role_name}")
            
            # إخفاء شاشة تسجيل الدخول وإظهار النظام الرئيسي
            show_main_system()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة.")
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

def show_main_system():
    """إظهار النظام الرئيسي"""
    # إخفاء شاشة تسجيل الدخول
    login_frame.pack_forget()
    
    # إظهار الشريط العلوي
    top_bar.pack(side="top", fill="x")
    
    # تحديث معلومات المستخدم
    username = current_user['username']
    role = current_user['role']
    role_name = "مدير النظام" if role == 'admin' else "مستخدم محدود"
    user_info_label.config(text=f"👤 {username} ({role_name})")
    
    # إظهار القائمة الجانبية
    sidebar_frame.pack(side="left", fill="y")
    
    # إنشاء أزرار القائمة
    create_menu_buttons()
    
    # إظهار المحتوى الرئيسي
    main_content.pack(side="right", fill="both", expand=True)
    
    # عرض لوحة التحكم
    show_dashboard()

def create_menu_buttons():
    """إنشاء أزرار القائمة"""
    # مسح الأزرار الموجودة
    for widget in sidebar_frame.winfo_children():
        if isinstance(widget, tk.Button):
            widget.destroy()
    
    # أزرار أساسية لجميع المستخدمين
    basic_buttons = [
        ("📊 لوحة التحكم", show_dashboard),
        ("📦 المخزون", show_inventory),
        ("🛒 المبيعات", show_sales),
    ]
    
    # أزرار المدير فقط
    admin_buttons = [
        ("🧾 الفواتير", show_invoices),
        ("👥 المستخدمين", show_users),
    ]
    
    # إضافة الأزرار الأساسية
    all_buttons = basic_buttons.copy()
    
    # إضافة أزرار المدير إذا كان المستخدم مدير
    if current_user.get('role') == 'admin':
        all_buttons.extend(admin_buttons)
    
    # إضافة زر تسجيل الخروج
    all_buttons.append(("🚪 تسجيل خروج", logout))
    
    # إنشاء الأزرار
    for text, command in all_buttons:
        btn = tk.Button(sidebar_frame, text=text, command=command,
                       bg=COLORS['bg_sidebar'], fg=COLORS['text_white'],
                       font=FONTS['main'], relief="flat", anchor="w",
                       padx=20, pady=10, width=20)
        btn.pack(fill="x", pady=2, padx=5)
        
        # تأثير hover
        def on_enter(event, button=btn):
            button.configure(bg=COLORS['accent'])
        
        def on_leave(event, button=btn):
            button.configure(bg=COLORS['bg_sidebar'])
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)

def clear_main_content():
    """مسح المحتوى الرئيسي"""
    for widget in main_content.winfo_children():
        widget.destroy()

def show_dashboard():
    """عرض لوحة التحكم"""
    clear_main_content()
    
    # عنوان الصفحة
    title_label = tk.Label(main_content, text="📊 لوحة التحكم", 
                          font=FONTS['title'], bg=COLORS['bg_main'], fg=COLORS['primary'])
    title_label.pack(pady=20)
    
    # رسالة ترحيب
    welcome_label = tk.Label(main_content, text=f"مرحباً بك {current_user['username']}", 
                            font=FONTS['heading'], bg=COLORS['bg_main'], fg=COLORS['text_primary'])
    welcome_label.pack(pady=10)
    
    # إحصائيات سريعة
    stats_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    stats_frame.pack(pady=20)
    
    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            
            # عدد المنتجات
            cursor.execute("SELECT COUNT(*) FROM products")
            products_count = cursor.fetchone()[0] if cursor.fetchone() else 0
            
            # عدد المبيعات
            cursor.execute("SELECT COUNT(*) FROM sales")
            sales_count = cursor.fetchone()[0] if cursor.fetchone() else 0
            
            cursor.close()
            db.close()
            
            # عرض الإحصائيات
            tk.Label(stats_frame, text=f"📦 المنتجات: {products_count}", 
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
            tk.Label(stats_frame, text=f"🛒 المبيعات: {sales_count}", 
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
    except:
        tk.Label(stats_frame, text="النظام يعمل بشكل صحيح!", 
                font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)

def show_inventory():
    """عرض المخزون"""
    clear_main_content()
    tk.Label(main_content, text="📦 إدارة المخزون", 
            font=FONTS['title'], bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=20)
    tk.Label(main_content, text="صفحة المخزون قيد التطوير", 
            font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=10)

def show_sales():
    """عرض المبيعات"""
    clear_main_content()
    tk.Label(main_content, text="🛒 إدارة المبيعات", 
            font=FONTS['title'], bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=20)
    tk.Label(main_content, text="صفحة المبيعات قيد التطوير", 
            font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=10)

def show_invoices():
    """عرض الفواتير"""
    clear_main_content()
    tk.Label(main_content, text="🧾 إدارة الفواتير", 
            font=FONTS['title'], bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=20)
    tk.Label(main_content, text="صفحة الفواتير قيد التطوير", 
            font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=10)

def show_users():
    """عرض المستخدمين"""
    clear_main_content()
    tk.Label(main_content, text="👥 إدارة المستخدمين", 
            font=FONTS['title'], bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=20)
    tk.Label(main_content, text="صفحة المستخدمين قيد التطوير", 
            font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=10)

def logout():
    """تسجيل الخروج"""
    # مسح معلومات المستخدم
    current_user['username'] = ''
    current_user['role'] = ''
    
    # إخفاء النظام الرئيسي
    top_bar.pack_forget()
    sidebar_frame.pack_forget()
    main_content.pack_forget()
    
    # مسح حقول تسجيل الدخول
    username_entry.delete(0, tk.END)
    password_entry.delete(0, tk.END)
    username_entry.insert(0, "اسم المستخدم")
    password_entry.insert(0, "كلمة المرور")
    username_entry.config(fg='gray')
    password_entry.config(fg='gray')
    
    # إظهار شاشة تسجيل الدخول
    login_frame.pack(fill="both", expand=True)
    
    messagebox.showinfo("تسجيل خروج", "تم تسجيل الخروج بنجاح")

def on_entry_click(event, entry, placeholder):
    """عند النقر على حقل الإدخال"""
    if entry.get() == placeholder:
        entry.delete(0, tk.END)
        entry.config(fg='black')
        if placeholder == "كلمة المرور":
            entry.config(show="*")

def on_entry_leave(event, entry, placeholder):
    """عند ترك حقل الإدخال"""
    if entry.get() == '':
        entry.insert(0, placeholder)
        entry.config(fg='gray')
        if placeholder == "كلمة المرور":
            entry.config(show="")

# ==== إنشاء النافذة الرئيسية ====
root = tk.Tk()
root.title("🏥 صيدلية الشفاء - نظام إدارة شامل")
root.geometry("1200x700")
root.configure(bg=COLORS['bg_main'])

# ==== شاشة تسجيل الدخول ====
login_frame = tk.Frame(root, bg=COLORS['bg_main'])

# إطار تسجيل الدخول المركزي
login_container = tk.Frame(login_frame, bg=COLORS['bg_card'], relief="raised", bd=2)
login_container.place(relx=0.5, rely=0.5, anchor="center", width=400, height=350)

# شعار وعنوان
tk.Label(login_container, text="🏥 صيدلية الشفاء",
         font=FONTS['title'], bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)

tk.Label(login_container, text="نظام إدارة شامل ومتطور",
         font=FONTS['main'], bg=COLORS['bg_card'], fg=COLORS['text_secondary']).pack(pady=5)

tk.Label(login_container, text="تسجيل الدخول",
         font=FONTS['heading'], bg=COLORS['bg_card'], fg=COLORS['text_secondary']).pack(pady=15)

# حقول الإدخال
username_entry = tk.Entry(login_container, font=FONTS['main'], width=25, relief="solid", bd=1)
username_entry.pack(pady=10)
username_entry.insert(0, "اسم المستخدم")
username_entry.config(fg='gray')

password_entry = tk.Entry(login_container, font=FONTS['main'], width=25, relief="solid", bd=1)
password_entry.pack(pady=10)
password_entry.insert(0, "كلمة المرور")
password_entry.config(fg='gray')

# ربط الأحداث
username_entry.bind('<FocusIn>', lambda event: on_entry_click(event, username_entry, "اسم المستخدم"))
username_entry.bind('<FocusOut>', lambda event: on_entry_leave(event, username_entry, "اسم المستخدم"))
password_entry.bind('<FocusIn>', lambda event: on_entry_click(event, password_entry, "كلمة المرور"))
password_entry.bind('<FocusOut>', lambda event: on_entry_leave(event, password_entry, "كلمة المرور"))

# ربط Enter
username_entry.bind("<Return>", lambda event: password_entry.focus())
password_entry.bind("<Return>", lambda event: login())

# زر تسجيل الدخول
login_btn = tk.Button(login_container, text="👤 دخول", command=login,
                     bg=COLORS['btn_success'], fg=COLORS['text_white'],
                     font=FONTS['button'], width=20, height=2, relief="flat")
login_btn.pack(pady=20)

# معلومات تسجيل الدخول
info_label = tk.Label(login_container, text="المدير: admin / admin123\nالمستخدم: user / user123",
                     font=('Arial', 9), bg=COLORS['bg_card'], fg=COLORS['text_secondary'])
info_label.pack(pady=10)

# ==== الشريط العلوي (مخفي في البداية) ====
top_bar = tk.Frame(root, bg=COLORS['primary'], height=40)
top_bar.pack_propagate(False)

# معلومات المستخدم
user_info_label = tk.Label(top_bar, text="👤 غير مسجل دخول", 
                          font=FONTS['main'], bg=COLORS['primary'], fg=COLORS['text_white'])
user_info_label.pack(side="right", padx=20, pady=8)

# عنوان النظام
system_title = tk.Label(top_bar, text="🏥 صيدلية الشفاء - نظام إدارة شامل", 
                       font=FONTS['heading'], bg=COLORS['primary'], fg=COLORS['text_white'])
system_title.pack(side="left", padx=20, pady=8)

# ==== القائمة الجانبية (مخفية في البداية) ====
sidebar_frame = tk.Frame(root, bg=COLORS['bg_sidebar'], width=250)
sidebar_frame.pack_propagate(False)

# شعار في القائمة الجانبية
logo_frame = tk.Frame(sidebar_frame, bg=COLORS['bg_sidebar'], height=80)
logo_frame.pack(fill="x", pady=10)
logo_frame.pack_propagate(False)

tk.Label(logo_frame, text="🏥", font=('Arial', 28),
         bg=COLORS['bg_sidebar'], fg=COLORS['text_white']).pack()
tk.Label(logo_frame, text="صيدلية الشفاء", font=('Arial', 14, 'bold'),
         bg=COLORS['bg_sidebar'], fg=COLORS['text_white']).pack()

# ==== المحتوى الرئيسي (مخفي في البداية) ====
main_content = tk.Frame(root, bg=COLORS['bg_main'])

# ==== بدء النظام بشاشة تسجيل الدخول ====
login_frame.pack(fill="both", expand=True)

print("🚀 تم تشغيل نظام صيدلية الشفاء")
print("📝 بيانات تسجيل الدخول:")
print("   👑 المدير: admin / admin123")
print("   👤 المستخدم: user / user123")

root.mainloop()
