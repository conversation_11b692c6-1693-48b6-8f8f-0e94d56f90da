# دليل المستخدم - نظام إدارة الصيدلية

## 🚀 البدء السريع

### تشغيل النظام
```bash
python main.py
```

### تسجيل الدخول
- أدخل اسم المستخدم وكلمة المرور
- اضغط على زر "دخول"

## 📦 إدارة المخزون

### إضافة منتج جديد
1. اختر "المخزون" من القائمة الجانبية
2. املأ جميع الحقول:
   - اسم المنتج (مطلوب)
   - الفئة (مطلوب)
   - سعر الجملة (رقم موجب)
   - سعر البيع (أكبر من سعر الجملة)
   - الكمية (رقم صحيح موجب)
   - تاريخ الانتهاء (MM/YYYY)
3. اضغط "➕ إضافة منتج"

### تحديث منتج
1. اختر المنتج من الجدول
2. ستظهر بياناته في النموذج
3. عدّل البيانات المطلوبة
4. اضغط "✏️ تحديث"

### حذف منتج
1. اختر المنتج من الجدول
2. اضغط "🗑️ حذف"
3. أكد الحذف

## 🛒 المبيعات وإنشاء الفواتير

### إضافة منتجات للسلة
1. اختر "المبيعات" من القائمة
2. اختر المنتج من القائمة المنسدلة
3. سيظهر السعر تلقائياً
4. أدخل الكمية المطلوبة
5. اضغط "➕ إضافة للسلة"

### إدخال بيانات العميل
- اسم العميل (مطلوب)
- رقم الهاتف (اختياري)
- طريقة الدفع (نقدي/بطاقة ائتمان/تحويل بنكي)

### إنشاء الفاتورة
1. تأكد من وجود منتجات في السلة
2. أدخل بيانات العميل
3. اضغط "🧾 إنشاء فاتورة"
4. ستفتح الفاتورة تلقائياً للطباعة

### حذف من السلة
- اختر المنتج من السلة
- اضغط "🗑️ حذف من السلة"

## 🧾 إدارة الفواتير

### عرض جميع الفواتير
- اختر "الفواتير" من القائمة الجانبية
- ستظهر جميع الفواتير مرتبة حسب التاريخ

### البحث في الفواتير
1. أدخل اسم العميل أو رقم الفاتورة
2. اضغط "🔍 بحث"
3. لمسح البحث اضغط "🔄 مسح"

### طباعة فاتورة سابقة
1. اختر الفاتورة من الجدول
2. اضغط "🖨️ طباعة الفاتورة"
3. ستفتح الفاتورة للطباعة

## 📊 لوحة التحكم

### الإحصائيات المتاحة
- **إجمالي المنتجات**: عدد أنواع المنتجات + إجمالي المخزون
- **قيمة المخزون**: القيمة الإجمالية للمخزون
- **الفواتير**: عدد الفواتير المنشأة
- **مبيعات اليوم**: مبيعات اليوم الحالي
- **إجمالي المبيعات**: جميع المبيعات
- **المستخدمين**: عدد المستخدمين المسجلين

## ⚠️ رسائل التحذير والأخطاء

### رسائل النجاح (✅)
- تظهر عند إتمام العمليات بنجاح
- مثل: "تمت إضافة المنتج بنجاح!"

### رسائل الخطأ (❌)
- تظهر عند وجود خطأ في البيانات
- تحتوي على تفاصيل الخطأ وكيفية إصلاحه

### رسائل التحذير (⚠️)
- تظهر للتنبيه قبل تنفيذ عمليات مهمة
- مثل: "السلة فارغة! يرجى إضافة منتجات أولاً"

## 🔧 نصائح مهمة

### عند إدخال البيانات:
- تأكد من صحة جميع البيانات قبل الحفظ
- استخدم تنسيق التاريخ MM/YYYY للانتهاء
- تأكد من أن سعر البيع أكبر من سعر الجملة

### عند البيع:
- تحقق من توفر الكمية المطلوبة
- أدخل بيانات العميل بدقة
- احتفظ بنسخة من الفاتورة المطبوعة

### للحصول على أفضل أداء:
- أغلق النوافذ غير المستخدمة
- احفظ نسخ احتياطية من قاعدة البيانات
- تأكد من اتصال قاعدة البيانات

## 🎨 الألوان والرموز

### الألوان:
- **أزرق**: معلومات عامة
- **أخضر**: نجاح العمليات
- **أحمر**: أخطاء ومشاكل
- **برتقالي**: تحذيرات
- **بنفسجي**: إحصائيات خاصة

### الأيقونات:
- ➕ إضافة
- ✏️ تحديث/تعديل
- 🗑️ حذف
- 🔍 بحث
- 🖨️ طباعة
- 🔄 تحديث/مسح
- 📦 مخزون
- 🛒 مبيعات
- 🧾 فواتير

## 🆘 حل المشاكل الشائعة

### "خطأ في الاتصال بقاعدة البيانات"
- تأكد من تشغيل خادم MySQL
- تحقق من بيانات الاتصال في الكود

### "المنتج غير موجود في المخزون"
- تأكد من إضافة المنتج في قسم المخزون أولاً
- تحديث قائمة المنتجات

### "الكمية المطلوبة أكبر من المتوفر"
- تحقق من الكمية المتاحة في المخزون
- قم بتحديث المخزون إذا لزم الأمر

### مشاكل الطباعة
- تأكد من وجود برنامج لفتح الملفات النصية
- تحقق من إعدادات الطابعة

---
**للمساعدة الإضافية، راجع ملف README_IMPROVEMENTS.md**
