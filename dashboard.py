# -*- coding: utf-8 -*-
import tkinter as tk
import mysql.connector
from datetime import datetime, timedelta
import sys
from theme import COLORS, FONTS, ICONS
from interface_manager import register_dashboard_interface

# إعداد الترميز للنصوص العربية
if sys.platform.startswith('win'):
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'Arabic_Saudi Arabia.1256')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'ar_SA.UTF-8')
        except:
            pass

# متغيرات عامة لتخزين مراجع البطاقات
dashboard_cards = {}
dashboard_frame_ref = None

# ==== الاتصال بقاعدة البيانات ====
def connect_db():
    return mysql.connector.connect(
        host="localhost",
        user="root",
        password="",
        database="pharmacy_db"
    )

def refresh_dashboard_now(frame=None):
    """تحديث فوري للوحة التحكم"""
    global dashboard_cards, dashboard_frame_ref

    if frame:
        dashboard_frame_ref = frame

    try:
        # جلب البيانات المحدثة
        db = connect_db()
        cursor = db.cursor()

        # إحصائيات المنتجات
        cursor.execute("SELECT COUNT(*) FROM inventory")
        total_products = cursor.fetchone()[0]

        cursor.execute("SELECT SUM(quantity) FROM inventory")
        total_stock = cursor.fetchone()[0] or 0

        cursor.execute("SELECT SUM(selling_price * quantity) FROM inventory")
        total_value = cursor.fetchone()[0] or 0

        # إحصائيات المبيعات
        cursor.execute("SELECT COUNT(DISTINCT invoice_id) FROM sales WHERE invoice_id IS NOT NULL")
        total_invoices = cursor.fetchone()[0] or 0

        cursor.execute("SELECT SUM(total) FROM sales WHERE DATE(date) = CURDATE()")
        today_sales = cursor.fetchone()[0] or 0

        cursor.execute("SELECT SUM(total) FROM sales")
        total_sales_value = cursor.fetchone()[0] or 0

        cursor.execute("SELECT COUNT(DISTINCT customer) FROM sales WHERE customer IS NOT NULL AND customer != ''")
        total_customers = cursor.fetchone()[0] or 0

        cursor.close()
        db.close()

        # تحديث البطاقات
        update_card_value('products', total_products)
        update_card_value('value', f"Ghc {total_value:.2f}")
        update_card_value('invoices', total_invoices)
        update_card_value('today_sales', f"Ghc {today_sales:.2f}")
        update_card_value('total_sales', f"Ghc {total_sales_value:.2f}")
        update_card_value('customers', total_customers)

        print("✅ تم تحديث لوحة التحكم بنجاح")

        # إظهار رسالة نجاح للمستخدم
        show_update_success_message()

    except Exception as e:
        print(f"❌ خطأ في تحديث لوحة التحكم: {e}")
        show_update_error_message(str(e))

def update_card_value(card_name, new_value):
    """تحديث قيمة بطاقة محددة مع تأثير بصري"""
    if card_name in dashboard_cards:
        try:
            # حفظ اللون الأصلي
            original_color = dashboard_cards[card_name].cget("fg")

            # تغيير اللون للإشارة للتحديث
            dashboard_cards[card_name].config(fg="#e74c3c")  # أحمر
            dashboard_cards[card_name].update()

            # تحديث القيمة
            dashboard_cards[card_name].config(text=str(new_value))
            dashboard_cards[card_name].update()

            # إعادة اللون الأصلي بعد 500ms
            dashboard_cards[card_name].after(500,
                lambda: dashboard_cards[card_name].config(fg=original_color))

        except Exception as e:
            print(f"خطأ في تحديث بطاقة {card_name}: {e}")

def show_update_success_message():
    """إظهار رسالة نجاح التحديث مع تأثيرات بصرية"""
    try:
        from tkinter import messagebox
        import winsound

        # إشعار صوتي (Windows فقط)
        try:
            winsound.MessageBeep(winsound.MB_OK)
        except:
            pass

        # رسالة نجاح
        messagebox.showinfo("✅ تم التحديث",
                           "تم تحديث بيانات لوحة التحكم بنجاح!\n\n"
                           "📊 جميع الإحصائيات محدثة\n"
                           "⚡ البيانات الآن دقيقة ومحدثة")
    except Exception as e:
        print(f"خطأ في إظهار رسالة النجاح: {e}")

def show_update_error_message(error_msg):
    """إظهار رسالة خطأ التحديث"""
    try:
        from tkinter import messagebox
        import winsound

        # إشعار صوتي للخطأ
        try:
            winsound.MessageBeep(winsound.MB_ICONHAND)
        except:
            pass

        messagebox.showerror("❌ خطأ في التحديث",
                            f"حدث خطأ أثناء تحديث لوحة التحكم:\n\n"
                            f"تفاصيل الخطأ: {error_msg}\n\n"
                            f"💡 نصائح:\n"
                            f"• تأكد من اتصال قاعدة البيانات\n"
                            f"• أعد المحاولة بعد قليل\n"
                            f"• تواصل مع الدعم التقني إذا استمر الخطأ")
    except Exception as e:
        print(f"خطأ في إظهار رسالة الخطأ: {e}")

# ==== نافذة لوحة التحكم المحسنة ====
def DashboardWindowd(root):
    frame = tk.Frame(root, bg=COLORS['bg_main'])

    # إطار العنوان مع تصميم أنيق
    header_frame = tk.Frame(frame, bg=COLORS['primary'], height=80)
    header_frame.pack(fill="x", pady=(0, 30))
    header_frame.pack_propagate(False)

    # إطار العنوان مع زر التحديث
    header_content_frame = tk.Frame(header_frame, bg=COLORS['primary'])
    header_content_frame.pack(expand=True, fill="both")

    tk.Label(header_content_frame, text=f"{ICONS['dashboard']} صيدلية الشفاء - لوحة التحكم",
             font=FONTS['title'], bg=COLORS['primary'], fg=COLORS['text_white']).pack(side="left", expand=True)

    # زر التحديث اليدوي المحسن
    def on_refresh_click():
        """معالج النقر على زر التحديث مع مؤشر تقدم"""
        try:
            # تغيير نص الزر أثناء التحديث
            refresh_btn.config(text="⏳ جاري التحديث...", state="disabled", bg="#f39c12")
            refresh_btn.update()

            # مؤشر تقدم بصري
            progress_steps = ["⏳ جاري الاتصال...", "📊 جلب البيانات...", "🔄 تحديث البطاقات...", "✅ اكتمل!"]

            for i, step in enumerate(progress_steps):
                refresh_btn.config(text=step)
                refresh_btn.update()

                if i == 0:
                    # محاكاة الاتصال
                    frame.after(200)
                elif i == 1:
                    # تنفيذ التحديث الفعلي
                    refresh_dashboard_now(frame)
                elif i == 2:
                    # محاكاة تحديث البطاقات
                    frame.after(300)
                elif i == 3:
                    # إظهار الاكتمال
                    frame.after(500)

            # إعادة تعيين الزر
            refresh_btn.config(text="🔄 تحديث البيانات", state="normal", bg=COLORS['success'])

        except Exception as e:
            # إعادة تعيين الزر في حالة الخطأ
            refresh_btn.config(text="❌ خطأ - أعد المحاولة", state="normal", bg="#e74c3c")
            # إعادة النص الأصلي بعد 3 ثوانٍ
            frame.after(3000, lambda: refresh_btn.config(text="🔄 تحديث البيانات", bg=COLORS['success']))
            print(f"خطأ في معالج التحديث: {e}")

    refresh_btn = tk.Button(header_content_frame, text="🔄 تحديث البيانات",
                           command=on_refresh_click,
                           bg=COLORS['success'], fg="white", font=FONTS['button'],
                           relief="flat", cursor="hand2", width=18, height=2,
                           activebackground="#219a52", activeforeground="white")
    refresh_btn.pack(side="right", padx=20, pady=10)

    # إطار الإحصائيات الرئيسية
    main_stats_frame = tk.Frame(frame, bg=COLORS['bg_main'])
    main_stats_frame.pack(pady=20, padx=20, fill="x")

    def create_stat_card(parent, title, value, icon, color, description="", card_id=None):
        """إنشاء بطاقة إحصائية محسنة مع إمكانية التحديث"""
        card = tk.Frame(parent, bg=COLORS['bg_card'], relief="raised", bd=3, width=250, height=140)
        card.pack_propagate(False)

        # إطار العنوان مع الأيقونة
        header = tk.Frame(card, bg=color, height=40)
        header.pack(fill="x")
        header.pack_propagate(False)

        tk.Label(header, text=f"{icon} {title}", font=FONTS['button'],
                bg=color, fg=COLORS['text_white']).pack(expand=True)

        # القيمة الرئيسية (مع حفظ المرجع للتحديث)
        value_label = tk.Label(card, text=str(value), font=('Arial', 24, 'bold'),
                              bg=COLORS['bg_card'], fg=color)
        value_label.pack(pady=15)

        # حفظ مرجع التسمية للتحديث لاحقاً
        if card_id:
            dashboard_cards[card_id] = value_label

        # الوصف
        if description:
            tk.Label(card, text=description, font=FONTS['small'],
                    bg=COLORS['bg_card'], fg=COLORS['text_light']).pack()

        return card

    # جلب البيانات من قاعدة البيانات
    try:
        db = connect_db()
        cursor = db.cursor()

        # إحصائيات المنتجات
        cursor.execute("SELECT COUNT(*) FROM inventory")
        total_products = cursor.fetchone()[0]

        cursor.execute("SELECT SUM(quantity) FROM inventory")
        total_stock = cursor.fetchone()[0] or 0

        cursor.execute("SELECT SUM(selling_price * quantity) FROM inventory")
        total_value = cursor.fetchone()[0] or 0

        # إحصائيات المبيعات
        cursor.execute("SELECT COUNT(DISTINCT invoice_id) FROM sales WHERE invoice_id IS NOT NULL")
        total_invoices = cursor.fetchone()[0] or 0

        cursor.execute("SELECT SUM(total) FROM sales WHERE DATE(date) = CURDATE()")
        today_sales = cursor.fetchone()[0] or 0

        cursor.execute("SELECT SUM(total) FROM sales")
        total_sales_value = cursor.fetchone()[0] or 0

        # إحصائيات المستخدمين
        cursor.execute("SELECT COUNT(*) FROM users")
        total_users = cursor.fetchone()[0]

        cursor.close()
        db.close()
    except Exception as e:
        total_products = total_stock = total_value = 0
        total_invoices = today_sales = total_sales_value = 0
        total_users = 0

    # الصف الأول من البطاقات
    row1_frame = tk.Frame(main_stats_frame, bg=COLORS['bg_main'])
    row1_frame.pack(fill="x", pady=10)

    card1 = create_stat_card(row1_frame, "إجمالي المنتجات", total_products,
                            ICONS['inventory'], COLORS['info'], f"المخزون: {total_stock} قطعة", "products")
    card1.pack(side="left", padx=15)

    card2 = create_stat_card(row1_frame, "قيمة المخزون", f"Ghc {total_value:.2f}",
                            ICONS['money'], COLORS['success'], "القيمة الإجمالية", "value")
    card2.pack(side="left", padx=15)

    card3 = create_stat_card(row1_frame, "الفواتير", total_invoices,
                            ICONS['invoice'], COLORS['purple'], "إجمالي الفواتير", "invoices")
    card3.pack(side="left", padx=15)

    # الصف الثاني من البطاقات
    row2_frame = tk.Frame(main_stats_frame, bg=COLORS['bg_main'])
    row2_frame.pack(fill="x", pady=10)

    card4 = create_stat_card(row2_frame, "مبيعات اليوم", f"Ghc {today_sales:.2f}",
                            ICONS['sales'], COLORS['warning'], "مبيعات اليوم الحالي", "today_sales")
    card4.pack(side="left", padx=15)

    card5 = create_stat_card(row2_frame, "إجمالي المبيعات", f"Ghc {total_sales_value:.2f}",
                            ICONS['stats'], COLORS['teal'], "جميع المبيعات", "total_sales")
    card5.pack(side="left", padx=15)

    card6 = create_stat_card(row2_frame, "المستخدمين", total_users,
                            ICONS['user'], COLORS['secondary'], "عدد المستخدمين", "customers")
    card6.pack(side="left", padx=15)

    # إطار الأنشطة الحديثة
    activity_frame = tk.Frame(frame, bg=COLORS['bg_card'], relief="raised", bd=2)
    activity_frame.pack(pady=30, padx=20, fill="both", expand=True)

    tk.Label(activity_frame, text=f"{ICONS['stats']} الأنشطة الحديثة",
             font=FONTS['heading'], bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(pady=15)

    # يمكن إضافة قائمة بالأنشطة الحديثة هنا
    tk.Label(activity_frame, text="لا توجد أنشطة حديثة",
             font=FONTS['main'], bg=COLORS['bg_card'], fg=COLORS['text_light']).pack(pady=20)

    # تسجيل لوحة التحكم في مدير الواجهات
    register_dashboard_interface(frame)

    return frame
