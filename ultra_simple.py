# -*- coding: utf-8 -*-
"""
نسخة بسيطة جداً من نظام صيدلية الشفاء
"""

import tkinter as tk
from tkinter import messagebox
import mysql.connector

# إنشاء النافذة الرئيسية
root = tk.Tk()
root.title("صيدلية الشفاء")
root.geometry("800x600")
root.configure(bg='lightgray')

# دالة الاتصال بقاعدة البيانات
def test_database():
    try:
        db = mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="pharmacy_db"
        )
        cursor = db.cursor()
        cursor.execute("SELECT COUNT(*) FROM users")
        count = cursor.fetchone()[0]
        cursor.close()
        db.close()
        messagebox.showinfo("نتيجة الاختبار", f"الاتصال ناجح!\nعدد المستخدمين: {count}")
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل الاتصال: {str(e)}")

# دالة تسجيل الدخول
def login():
    username = entry_user.get()
    password = entry_pass.get()
    
    if username == "admin" and password == "admin123":
        messagebox.showinfo("نجح", "مرحباً بك أيها المدير!")
        show_buttons()
    elif username == "user" and password == "user123":
        messagebox.showinfo("نجح", "مرحباً بك أيها المستخدم!")
        show_buttons()
    else:
        messagebox.showerror("خطأ", "بيانات خاطئة!")

# دالة إظهار الأزرار
def show_buttons():
    # إخفاء شاشة تسجيل الدخول
    login_frame.pack_forget()
    
    # إظهار الأزرار
    buttons_frame.pack(fill="both", expand=True)

# دالة لوحة التحكم
def dashboard():
    result_label.config(text="لوحة التحكم\nمرحباً بك في النظام", fg="blue")

# دالة المخزون
def inventory():
    result_label.config(text="إدارة المخزون\nهنا يمكنك إدارة المنتجات", fg="green")

# دالة المبيعات
def sales():
    result_label.config(text="إدارة المبيعات\nهنا يمكنك تسجيل المبيعات", fg="orange")

# دالة الخروج
def logout():
    buttons_frame.pack_forget()
    login_frame.pack(fill="both", expand=True)
    entry_user.delete(0, tk.END)
    entry_pass.delete(0, tk.END)

# إنشاء شاشة تسجيل الدخول
login_frame = tk.Frame(root, bg='lightblue')
login_frame.pack(fill="both", expand=True)

tk.Label(login_frame, text="صيدلية الشفاء", font=('Arial', 20, 'bold'), bg='lightblue').pack(pady=50)
tk.Label(login_frame, text="تسجيل الدخول", font=('Arial', 16), bg='lightblue').pack(pady=20)

tk.Label(login_frame, text="اسم المستخدم:", font=('Arial', 12), bg='lightblue').pack()
entry_user = tk.Entry(login_frame, font=('Arial', 14), width=20)
entry_user.pack(pady=10)

tk.Label(login_frame, text="كلمة المرور:", font=('Arial', 12), bg='lightblue').pack()
entry_pass = tk.Entry(login_frame, font=('Arial', 14), width=20, show='*')
entry_pass.pack(pady=10)

tk.Button(login_frame, text="دخول", command=login, font=('Arial', 14, 'bold'), 
          bg='green', fg='white', width=15).pack(pady=20)

tk.Label(login_frame, text="admin / admin123  أو  user / user123", 
         font=('Arial', 10), bg='lightblue').pack()

# إنشاء إطار الأزرار (مخفي في البداية)
buttons_frame = tk.Frame(root, bg='lightgreen')

# الشريط العلوي
top_bar = tk.Frame(buttons_frame, bg='darkgreen', height=60)
top_bar.pack(fill='x')
top_bar.pack_propagate(False)

tk.Label(top_bar, text="صيدلية الشفاء - نظام الإدارة", font=('Arial', 16, 'bold'), 
         bg='darkgreen', fg='white').pack(pady=20)

# منطقة الأزرار
buttons_area = tk.Frame(buttons_frame, bg='lightgreen')
buttons_area.pack(fill='both', expand=True, padx=50, pady=30)

# الأزرار
button_data = [
    ("لوحة التحكم", dashboard, 'blue'),
    ("إدارة المخزون", inventory, 'green'),
    ("إدارة المبيعات", sales, 'orange'),
    ("اختبار قاعدة البيانات", test_database, 'purple'),
    ("تسجيل خروج", logout, 'red')
]

for text, command, color in button_data:
    btn = tk.Button(buttons_area, text=text, command=command, 
                   font=('Arial', 14, 'bold'), bg=color, fg='white',
                   width=20, height=2)
    btn.pack(pady=10)

# منطقة النتائج
result_label = tk.Label(buttons_area, text="اضغط على أي زر لبدء العمل", 
                       font=('Arial', 16), bg='lightgreen', fg='black')
result_label.pack(pady=30)

# تشغيل النظام
print("تم تشغيل النظام البسيط")
print("بيانات الدخول: admin/admin123 أو user/user123")

root.mainloop()
