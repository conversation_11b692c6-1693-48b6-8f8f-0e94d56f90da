# 🔄 دليل الأزرار المسترجعة - نظام صيدلية الشفاء

## 🎯 **الهدف:**
إرجاع جميع الأزرار التي كانت موجودة في النظام الأصلي والمتصلة بقاعدة البيانات.

## ✅ **الأزرار المسترجعة:**

### **1. 📊 لوحة التحكم (Dashboard):**
```
الأيقونة: 📊
النص: "📊 لوحة التحكم"
الوظيفة: عرض الإحصائيات والمعلومات العامة
الصلاحيات: جميع المستخدمين
الاتصال: متصل بقاعدة البيانات لعرض الإحصائيات
```

### **2. 📦 المخزون (Inventory):**
```
الأيقونة: 📦
النص: "📦 المخزون"
الوظيفة: إدارة المنتجات والأدوية
الصلاحيات: جميع المستخدمين
الاتصال: متصل بجدول products في قاعدة البيانات
```

### **3. 🛒 المبيعات (Sales):**
```
الأيقونة: 🛒
النص: "🛒 المبيعات"
الوظيفة: تسجيل المبيعات وإدارة العمليات التجارية
الصلاحيات: جميع المستخدمين
الاتصال: متصل بجدول sales في قاعدة البيانات
```

### **4. 🧾 الفواتير (Invoice) - للمدير فقط:**
```
الأيقونة: 🧾
النص: "🧾 الفواتير"
الوظيفة: إدارة الفواتير وطباعتها
الصلاحيات: المدير فقط
الاتصال: متصل بجدول invoices في قاعدة البيانات
```

### **5. 👤 المستخدمين (Users) - للمدير فقط:**
```
الأيقونة: 👤
النص: "👤 المستخدمين"
الوظيفة: إدارة المستخدمين والصلاحيات
الصلاحيات: المدير فقط
الاتصال: متصل بجدول users في قاعدة البيانات
```

### **6. 🚪 تسجيل الخروج (Logout):**
```
الأيقونة: 🚪
النص: "🚪 تسجيل خروج"
الوظيفة: تسجيل الخروج من النظام
الصلاحيات: جميع المستخدمين
الاتصال: مسح بيانات الجلسة
```

## 🔧 **الكود المطبق:**

### **دالة إنشاء القائمة الجانبية:**
```python
def create_sidebar_menu():
    """إنشاء القائمة الجانبية حسب صلاحيات المستخدم"""
    # مسح الأزرار الموجودة
    for widget in dash_frame.winfo_children():
        if isinstance(widget, tk.Button):
            widget.destroy()

    # أزرار القائمة الأساسية (متاحة لجميع المستخدمين)
    basic_buttons = [
        (f"{ICONS['dashboard']} لوحة التحكم", lambda: show_page("Dashboard")),
        (f"{ICONS['inventory']} المخزون", lambda: show_page("Inventory")),
        (f"{ICONS['sales']} المبيعات", lambda: show_page("Sales")),
    ]

    # أزرار المدير فقط
    admin_buttons = [
        (f"{ICONS['invoice']} الفواتير", lambda: show_page("Invoice")),
        (f"{ICONS['user']} المستخدمين", lambda: show_page("Users")),
    ]

    # أزرار عامة
    common_buttons = [
        (f"{ICONS['logout']} تسجيل خروج", logout)
    ]

    # إضافة الأزرار الأساسية
    all_buttons = basic_buttons.copy()

    # إضافة أزرار المدير إذا كان المستخدم مدير
    if current_user.get('role') == 'admin':
        all_buttons.extend(admin_buttons)

    # إضافة الأزرار العامة
    all_buttons.extend(common_buttons)

    # إنشاء الأزرار
    for text, cmd in all_buttons:
        btn = tk.Button(dash_frame, text=text, command=cmd, font=FONTS['main'],
                       bg=COLORS['bg_sidebar'], fg=COLORS['text_white'],
                       relief="flat", anchor="w", padx=20, pady=8,
                       activebackground=COLORS['accent'], activeforeground=COLORS['text_white'])
        btn.pack(fill="x", pady=2, padx=5)

        # تأثير hover
        def on_enter(event, button=btn):
            button.configure(bg=COLORS['accent'])

        def on_leave(event, button=btn):
            button.configure(bg=COLORS['bg_sidebar'])

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
```

### **دالة عرض الصفحات:**
```python
def show_page(page_name):
    """عرض الصفحة المطلوبة"""
    # إخفاء جميع الصفحات
    for frame in [dashboard_frame, inventory_frame, sales_frame, users_frame, invoice_frame]:
        frame.pack_forget()
    
    # عرض الصفحة المطلوبة
    if page_name == "Dashboard":
        dashboard_frame.pack(fill="both", expand=True)
    elif page_name == "Inventory":
        inventory_frame.pack(fill="both", expand=True)
    elif page_name == "Sales":
        sales_frame.pack(fill="both", expand=True)
    elif page_name == "Users":
        users_frame.pack(fill="both", expand=True)
    elif page_name == "Invoice":
        invoice_frame.pack(fill="both", expand=True)
```

## 📱 **تجربة المستخدم:**

### **للمدير (admin):**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 🏥 صيدلية الشفاء - نظام إدارة شامل    👤 admin (مدير النظام)    │
├─────────────────────────────────────────────────────────────────────┤
│ 🏥 صيدلية الشفاء │ المحتوى الرئيسي                               │
│ Al-Shifa Pharmacy │                                                 │
│                   │                                                 │
│ 📊 لوحة التحكم    │ ← متاح                                         │
│ 📦 المخزون        │ ← متاح                                         │
│ 🛒 المبيعات       │ ← متاح                                         │
│ 🧾 الفواتير       │ ← متاح (للمدير فقط)                           │
│ 👤 المستخدمين     │ ← متاح (للمدير فقط)                           │
│ 🚪 تسجيل خروج     │ ← متاح                                         │
└─────────────────────────────────────────────────────────────────────┘
```

### **للمستخدم المحدود (user):**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 🏥 صيدلية الشفاء - نظام إدارة شامل    👤 user (مستخدم محدود)    │
├─────────────────────────────────────────────────────────────────────┤
│ 🏥 صيدلية الشفاء │ المحتوى الرئيسي                               │
│ Al-Shifa Pharmacy │                                                 │
│                   │                                                 │
│ 📊 لوحة التحكم    │ ← متاح                                         │
│ 📦 المخزون        │ ← متاح                                         │
│ 🛒 المبيعات       │ ← متاح                                         │
│ 🚪 تسجيل خروج     │ ← متاح                                         │
│                   │                                                 │
│ (الفواتير والمستخدمين مخفية)                                      │
└─────────────────────────────────────────────────────────────────────┘
```

## 🔗 **الاتصال بقاعدة البيانات:**

### **الجداول المتصلة:**

#### **1. جدول المنتجات (products):**
```sql
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    quantity INT NOT NULL,
    category VARCHAR(100),
    expiry_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **2. جدول المبيعات (sales):**
```sql
CREATE TABLE sales (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT,
    quantity INT NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id)
);
```

#### **3. جدول الفواتير (invoices):**
```sql
CREATE TABLE invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_name VARCHAR(255),
    total_amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50),
    invoice_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **4. جدول المستخدمين (users):**
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user') NOT NULL DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🎨 **التصميم والألوان:**

### **ألوان الأزرار:**
```python
# الحالة العادية
bg=COLORS['bg_sidebar']      # #2c3e50 (رمادي داكن)
fg=COLORS['text_white']      # #ffffff (أبيض)

# عند التمرير (hover)
bg=COLORS['accent']          # #3498db (أزرق فاتح)
fg=COLORS['text_white']      # #ffffff (أبيض)

# عند النقر (active)
activebackground=COLORS['accent']     # #3498db
activeforeground=COLORS['text_white'] # #ffffff
```

### **خصائص الأزرار:**
```python
font=FONTS['main']           # Arial, 12
relief="flat"                # بدون حدود بارزة
anchor="w"                   # محاذاة لليسار
padx=20, pady=8             # مساحة داخلية
```

## 🚀 **كيفية الاستخدام:**

### **تسجيل الدخول كمدير:**
```
1. اسم المستخدم: admin
2. كلمة المرور: admin123
3. ستجد جميع الأزرار (6 أزرار)
```

### **تسجيل الدخول كمستخدم محدود:**
```
1. اسم المستخدم: user
2. كلمة المرور: user123
3. ستجد الأزرار الأساسية فقط (4 أزرار)
```

### **التنقل بين الصفحات:**
```
1. اضغط على أي زر في القائمة الجانبية
2. ستنتقل للصفحة المطلوبة
3. المحتوى يتغير في المنطقة الرئيسية
4. القائمة الجانبية تبقى ثابتة
```

## 📊 **الوظائف المتاحة:**

### **📊 لوحة التحكم:**
- عرض إحصائيات المبيعات
- عرض عدد المنتجات
- عرض قيمة المخزون
- عرض عدد الفواتير

### **📦 المخزون:**
- إضافة منتجات جديدة
- تعديل المنتجات الموجودة
- حذف المنتجات
- البحث في المنتجات
- عرض تواريخ الانتهاء

### **🛒 المبيعات:**
- تسجيل مبيعات جديدة
- إضافة منتجات للسلة
- حساب الإجمالي
- اختيار طريقة الدفع
- طباعة الفاتورة

### **🧾 الفواتير (للمدير فقط):**
- عرض جميع الفواتير
- البحث في الفواتير
- طباعة الفواتير
- إحصائيات الفواتير

### **👤 المستخدمين (للمدير فقط):**
- إضافة مستخدمين جدد
- تعديل صلاحيات المستخدمين
- حذف المستخدمين
- عرض قائمة المستخدمين

### **🚪 تسجيل الخروج:**
- مسح بيانات الجلسة
- العودة لشاشة تسجيل الدخول
- تنظيف الواجهة

## 💡 **المزايا المسترجعة:**

### **1. 🎯 نظام الصلاحيات:**
- أزرار مختلفة حسب نوع المستخدم
- حماية الوظائف الحساسة
- واجهة ديناميكية

### **2. 🎨 التصميم المحسن:**
- تأثيرات hover جميلة
- ألوان متناسقة
- أيقونات واضحة

### **3. 🔗 الاتصال بقاعدة البيانات:**
- جميع الأزرار متصلة بالجداول
- عمليات CRUD كاملة
- استعلامات محسنة

### **4. 📱 سهولة الاستخدام:**
- تنقل سلس بين الصفحات
- واجهة بديهية
- استجابة سريعة

## 🎉 **النتيجة النهائية:**

### **تم إرجاع:**
```
✅ 📊 لوحة التحكم - متصل بقاعدة البيانات
✅ 📦 المخزون - متصل بجدول products
✅ 🛒 المبيعات - متصل بجدول sales
✅ 🧾 الفواتير - متصل بجدول invoices (للمدير فقط)
✅ 👤 المستخدمين - متصل بجدول users (للمدير فقط)
✅ 🚪 تسجيل الخروج - وظيفة كاملة
```

### **الميزات:**
```
✅ نظام صلاحيات ديناميكي
✅ تأثيرات بصرية جميلة
✅ اتصال كامل بقاعدة البيانات
✅ واجهة سهلة الاستخدام
✅ تنقل سلس بين الصفحات
```

---

**🔄 تم إرجاع جميع الأزرار بنجاح!**

**💡 تذكر**: الآن النظام يحتوي على جميع الأزرار الأصلية المتصلة بقاعدة البيانات مع نظام صلاحيات محسن!
