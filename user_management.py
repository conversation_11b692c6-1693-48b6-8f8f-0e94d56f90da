# -*- coding: utf-8 -*-
"""
نظام إدارة المستخدمين مع الصلاحيات المحدودة
User Management System with Limited Permissions
"""

import tkinter as tk
from tkinter import ttk, messagebox
import mysql.connector
from theme import COLORS, FONTS, ICONS

def connect_db():
    """الاتصال بقاعدة البيانات"""
    return mysql.connector.connect(
        host="localhost",
        user="root",
        password="",
        database="pharmacy_db"
    )

# ==== أنواع المستخدمين والصلاحيات ====
USER_ROLES = {
    'admin': {
        'name_ar': 'مدير النظام',
        'name_en': 'System Administrator',
        'permissions': ['dashboard', 'inventory', 'sales', 'invoice', 'users', 'reports'],
        'description': 'صلاحيات كاملة لجميع أجزاء النظام'
    },
    'user': {
        'name_ar': 'مستخدم محدود',
        'name_en': 'Limited User',
        'permissions': ['dashboard', 'inventory', 'sales'],
        'description': 'صلاحيات محدودة للعمليات الأساسية فقط'
    }
}

def get_user_permissions(role):
    """الحصول على صلاحيات المستخدم"""
    return USER_ROLES.get(role, {}).get('permissions', [])

def check_user_permission(user_role, required_permission):
    """التحقق من صلاحية المستخدم"""
    user_permissions = get_user_permissions(user_role)
    return required_permission in user_permissions

def create_user_management_page(parent_frame):
    """إنشاء صفحة إدارة المستخدمين المحسنة"""
    
    # مسح المحتوى السابق
    for widget in parent_frame.winfo_children():
        widget.destroy()
    
    # الإطار الرئيسي
    main_frame = tk.Frame(parent_frame, bg=COLORS['bg_main'])
    main_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    # العنوان
    title_frame = tk.Frame(main_frame, bg=COLORS['primary'], height=60)
    title_frame.pack(fill="x", pady=(0, 20))
    title_frame.pack_propagate(False)
    
    tk.Label(title_frame, text=f"{ICONS['user']} إدارة المستخدمين والصلاحيات",
             font=FONTS['title'], bg=COLORS['primary'], fg=COLORS['text_white']).pack(expand=True)
    
    # إطار النموذج
    form_frame = tk.LabelFrame(main_frame, text="إضافة مستخدم جديد", 
                              font=FONTS['heading'], bg=COLORS['bg_card'], 
                              fg=COLORS['text_primary'], padx=20, pady=15)
    form_frame.pack(fill="x", pady=(0, 20))
    
    # حقول الإدخال
    fields_frame = tk.Frame(form_frame, bg=COLORS['bg_card'])
    fields_frame.pack(fill="x")
    
    # اسم المستخدم
    tk.Label(fields_frame, text="اسم المستخدم:", font=FONTS['main'], 
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).grid(row=0, column=0, sticky="e", padx=10, pady=8)
    username_entry = tk.Entry(fields_frame, font=FONTS['main'], width=20, relief="solid", bd=1)
    username_entry.grid(row=0, column=1, padx=10, pady=8)
    
    # كلمة المرور
    tk.Label(fields_frame, text="كلمة المرور:", font=FONTS['main'], 
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).grid(row=0, column=2, sticky="e", padx=10, pady=8)
    password_entry = tk.Entry(fields_frame, font=FONTS['main'], width=20, relief="solid", bd=1, show="*")
    password_entry.grid(row=0, column=3, padx=10, pady=8)
    
    # نوع المستخدم
    tk.Label(fields_frame, text="نوع المستخدم:", font=FONTS['main'], 
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).grid(row=1, column=0, sticky="e", padx=10, pady=8)
    
    role_values = [f"{USER_ROLES[role]['name_ar']} ({role})" for role in USER_ROLES.keys()]
    role_combo = ttk.Combobox(fields_frame, values=role_values, width=25, font=FONTS['main'], state="readonly")
    role_combo.grid(row=1, column=1, padx=10, pady=8)
    role_combo.set(role_values[1])  # افتراضي: مستخدم محدود
    
    # وصف الصلاحيات
    permissions_label = tk.Label(fields_frame, text="", font=FONTS['small'], 
                                bg=COLORS['bg_card'], fg=COLORS['text_secondary'], wraplength=300)
    permissions_label.grid(row=1, column=2, columnspan=2, padx=10, pady=8, sticky="w")
    
    def update_permissions_description(event=None):
        """تحديث وصف الصلاحيات عند تغيير نوع المستخدم"""
        selected = role_combo.get()
        if selected:
            role_key = selected.split('(')[1].split(')')[0]
            description = USER_ROLES.get(role_key, {}).get('description', '')
            permissions_label.config(text=description)
    
    role_combo.bind('<<ComboboxSelected>>', update_permissions_description)
    update_permissions_description()  # تحديث أولي
    
    # أزرار العمليات
    buttons_frame = tk.Frame(form_frame, bg=COLORS['bg_card'])
    buttons_frame.pack(fill="x", pady=10)
    
    def clear_fields():
        """مسح الحقول"""
        username_entry.delete(0, tk.END)
        password_entry.delete(0, tk.END)
        role_combo.set(role_values[1])
        update_permissions_description()
    
    def add_user():
        """إضافة مستخدم جديد"""
        username = username_entry.get().strip()
        password = password_entry.get().strip()
        selected_role = role_combo.get()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return
        
        if len(username) < 3:
            messagebox.showerror("خطأ", "اسم المستخدم يجب أن يكون 3 أحرف على الأقل")
            return
        
        if len(password) < 4:
            messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 4 أحرف على الأقل")
            return
        
        # استخراج نوع المستخدم
        role_key = selected_role.split('(')[1].split(')')[0]
        
        try:
            db = connect_db()
            cursor = db.cursor()
            
            # التحقق من عدم وجود المستخدم
            cursor.execute("SELECT username FROM users WHERE username = %s", (username,))
            if cursor.fetchone():
                messagebox.showerror("خطأ", "اسم المستخدم موجود بالفعل")
                return
            
            # إضافة المستخدم
            cursor.execute("INSERT INTO users (username, password, role) VALUES (%s, %s, %s)", 
                          (username, password, role_key))
            db.commit()
            
            messagebox.showinfo("نجح", f"تم إضافة المستخدم '{username}' بنجاح")
            clear_fields()
            load_users()
            
        except mysql.connector.Error as e:
            messagebox.showerror("خطأ في قاعدة البيانات", f"حدث خطأ: {e}")
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'db' in locals():
                db.close()
    
    def delete_user():
        """حذف المستخدم المحدد"""
        selected_item = users_tree.selection()
        if not selected_item:
            messagebox.showwarning("تنبيه", "يرجى اختيار مستخدم للحذف")
            return
        
        user_data = users_tree.item(selected_item[0])['values']
        user_id = user_data[0]
        username = user_data[1]
        
        # منع حذف المدير الرئيسي
        if username == 'admin':
            messagebox.showerror("خطأ", "لا يمكن حذف المدير الرئيسي")
            return
        
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المستخدم '{username}'؟"):
            try:
                db = connect_db()
                cursor = db.cursor()
                cursor.execute("DELETE FROM users WHERE id = %s", (user_id,))
                db.commit()
                
                messagebox.showinfo("نجح", f"تم حذف المستخدم '{username}' بنجاح")
                load_users()
                
            except mysql.connector.Error as e:
                messagebox.showerror("خطأ في قاعدة البيانات", f"حدث خطأ: {e}")
            finally:
                if 'cursor' in locals():
                    cursor.close()
                if 'db' in locals():
                    db.close()
    
    # أزرار العمليات
    tk.Button(buttons_frame, text=f"{ICONS['add']} إضافة مستخدم", command=add_user,
              bg=COLORS['btn_success'], fg=COLORS['text_white'], font=FONTS['button'],
              width=15, height=2, relief="flat", cursor="hand2").pack(side="left", padx=5)
    
    tk.Button(buttons_frame, text=f"{ICONS['delete']} حذف مستخدم", command=delete_user,
              bg=COLORS['error'], fg=COLORS['text_white'], font=FONTS['button'],
              width=15, height=2, relief="flat", cursor="hand2").pack(side="left", padx=5)
    
    tk.Button(buttons_frame, text=f"{ICONS['clear']} مسح الحقول", command=clear_fields,
              bg=COLORS['warning'], fg=COLORS['text_white'], font=FONTS['button'],
              width=15, height=2, relief="flat", cursor="hand2").pack(side="left", padx=5)
    
    # جدول المستخدمين
    table_frame = tk.LabelFrame(main_frame, text="قائمة المستخدمين", 
                               font=FONTS['heading'], bg=COLORS['bg_card'], 
                               fg=COLORS['text_primary'], padx=10, pady=10)
    table_frame.pack(fill="both", expand=True)
    
    # إعداد الجدول
    columns = ("ID", "اسم المستخدم", "نوع المستخدم", "الصلاحيات")
    users_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=12)

    # تعيين عناوين الأعمدة
    users_tree.heading("ID", text="الرقم")
    users_tree.heading("اسم المستخدم", text="اسم المستخدم")
    users_tree.heading("نوع المستخدم", text="نوع المستخدم")
    users_tree.heading("الصلاحيات", text="الصلاحيات")

    # تعيين عرض الأعمدة
    users_tree.column("ID", width=80, anchor="center")
    users_tree.column("اسم المستخدم", width=150, anchor="center")
    users_tree.column("نوع المستخدم", width=150, anchor="center")
    users_tree.column("الصلاحيات", width=300, anchor="center")
    
    # شريط التمرير
    scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=users_tree.yview)
    users_tree.configure(yscrollcommand=scrollbar.set)
    
    users_tree.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    def load_users():
        """تحميل قائمة المستخدمين"""
        # مسح البيانات السابقة
        for item in users_tree.get_children():
            users_tree.delete(item)

        try:
            db = connect_db()
            cursor = db.cursor()

            # التحقق من وجود عمود created_at
            cursor.execute("SHOW COLUMNS FROM users LIKE 'created_at'")
            has_created_at = cursor.fetchone() is not None

            if has_created_at:
                cursor.execute("SELECT id, username, role, created_at FROM users ORDER BY created_at DESC")
            else:
                cursor.execute("SELECT id, username, role FROM users ORDER BY id DESC")

            for row in cursor.fetchall():
                if has_created_at:
                    user_id, username, role, created_at = row
                else:
                    user_id, username, role = row

                role_name = USER_ROLES.get(role, {}).get('name_ar', role)
                permissions_list = get_user_permissions(role)

                # ترجمة الصلاحيات للعربية
                permission_translations = {
                    'dashboard': 'لوحة التحكم',
                    'inventory': 'المخزون',
                    'sales': 'المبيعات',
                    'invoice': 'الفواتير',
                    'users': 'المستخدمين',
                    'reports': 'التقارير'
                }

                permissions_ar = [permission_translations.get(p, p) for p in permissions_list]
                permissions_text = ', '.join(permissions_ar)

                users_tree.insert("", "end", values=(user_id, username, role_name, permissions_text))

        except mysql.connector.Error as e:
            messagebox.showerror("خطأ في قاعدة البيانات", f"حدث خطأ في تحميل المستخدمين: {e}")
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'db' in locals():
                db.close()
    
    # تحميل المستخدمين عند فتح الصفحة
    load_users()
    
    return main_frame

# ==== دوال مساعدة للتصدير ====
def get_current_user_role():
    """الحصول على دور المستخدم الحالي"""
    # هذه الدالة ستستخدم من main.py
    pass

def validate_user_access(required_permission):
    """التحقق من صلاحية الوصول"""
    # هذه الدالة ستستخدم من main.py
    pass

# ==== تصدير الدوال والمتغيرات ====
__all__ = [
    'USER_ROLES',
    'get_user_permissions',
    'check_user_permission',
    'create_user_management_page'
]
