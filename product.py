import tkinter as tk
from tkinter import ttk, messagebox
import mysql.connector
from datetime import datetime

def connect_db():
    return mysql.connector.connect(
        host="localhost",
        user="root",
        password="your_password",  # ← غيّرها
        database="pharmacy_db"
    )

# البحث عن منتج
def search_product():
    name = search_entry.get()
    if not name:
        messagebox.showwarning("تنبيه", "يرجى إدخال اسم المنتج.")
        return

    db = connect_db()
    cursor = db.cursor()
    cursor.execute("SELECT product_name, selling_price, quantity FROM inventory WHERE product_name = %s", (name,))
    result = cursor.fetchone()
    cursor.close()
    db.close()

    if result:
        pname, price, stock = result
        pname_lbl.config(text=pname)
        price_lbl.config(text=f"{price:.2f}")
        stock_lbl.config(text=f"{stock}")
        qty_entry.delete(0, tk.END)
        total_lbl.config(text="0.00")
    else:
        messagebox.showerror("خطأ", "المنتج غير موجود.")

# حساب السعر الكلي
def calculate_total():
    try:
        unit_price = float(price_lbl.cget("text"))
        qty = int(qty_entry.get())
        total = unit_price * qty
        total_lbl.config(text=f"{total:.2f}")
    except:
        total_lbl.config(text="0.00")

# إضافة إلى السلة
def add_to_cart():
    try:
        pname = pname_lbl.cget("text")
        unit_price = float(price_lbl.cget("text"))
        qty = int(qty_entry.get())
        stock = int(stock_lbl.cget("text"))

        if qty > stock:
            messagebox.showerror("خطأ", "الكمية المطلوبة أكبر من المخزون المتوفر.")
            return

        total = unit_price * qty
        cart_tree.insert("", "end", values=(pname, qty, unit_price, total))
        update_total_invoice()

    except Exception as e:
        messagebox.showerror("خطأ", str(e))

# تحديث إجمالي الفاتورة
def update_total_invoice():
    total = 0
    for row in cart_tree.get_children():
        values = cart_tree.item(row, 'values')
        total += float(values[3])
    invoice_total_lbl.config(text=f"{total:.2f}")

# حفظ الفاتورة وتحديث المخزون
def finalize_sale():
    db = connect_db()
    cursor = db.cursor()

    try:
        for row in cart_tree.get_children():
            pname, qty, unit_price, total_price = cart_tree.item(row, 'values')

            # 1. تخزين البيع
            cursor.execute("""
                INSERT INTO sales (product_name, quantity_sold, unit_price, total_price)
                VALUES (%s, %s, %s, %s)
            """, (pname, qty, unit_price, total_price))

            # 2. تحديث المخزون
            cursor.execute("""
                UPDATE inventory SET quantity = quantity - %s WHERE product_name = %s
            """, (qty, pname))

        db.commit()
        messagebox.showinfo("تم", "تم حفظ الفاتورة.")
        cart_tree.delete(*cart_tree.get_children())
        invoice_total_lbl.config(text="0.00")
        search_entry.delete(0, tk.END)
        pname_lbl.config(text="")
        price_lbl.config(text="")
        stock_lbl.config(text="")
        qty_entry.delete(0, tk.END)
        total_lbl.config(text="0.00")

    except Exception as e:
        db.rollback()
        messagebox.showerror("خطأ", str(e))
    finally:
        cursor.close()
        db.close()

# واجهة المبيعات
root = tk.Tk()
root.title("واجهة المبيعات")
root.geometry("850x600")
root.configure(bg="#ecf0f1")

tk.Label(root, text="Sales Interface", font=("Helvetica", 16, "bold"), bg="#ecf0f1").pack(pady=10)

# ======= بحث المنتج =======
search_frame = tk.Frame(root, bg="#ecf0f1")
search_frame.pack(pady=5)

tk.Label(search_frame, text="Product Name:", bg="#ecf0f1").grid(row=0, column=0)
search_entry = tk.Entry(search_frame, width=30)
search_entry.grid(row=0, column=1)
tk.Button(search_frame, text="Search", bg="blue", fg="white", command=search_product).grid(row=0, column=2, padx=5)

# ======= معلومات المنتج =======
info_frame = tk.Frame(root, bg="#ecf0f1")
info_frame.pack(pady=10)

tk.Label(info_frame, text="Product:", bg="#ecf0f1").grid(row=0, column=0, sticky="e")
pname_lbl = tk.Label(info_frame, text="", width=20, bg="white")
pname_lbl.grid(row=0, column=1)

tk.Label(info_frame, text="Unit Price:", bg="#ecf0f1").grid(row=1, column=0, sticky="e")
price_lbl = tk.Label(info_frame, text="", width=20, bg="white")
price_lbl.grid(row=1, column=1)

tk.Label(info_frame, text="Available Stock:", bg="#ecf0f1").grid(row=2, column=0, sticky="e")
stock_lbl = tk.Label(info_frame, text="", width=20, bg="white")
stock_lbl.grid(row=2, column=1)

tk.Label(info_frame, text="Quantity:", bg="#ecf0f1").grid(row=3, column=0, sticky="e")
qty_entry = tk.Entry(info_frame, width=22)
qty_entry.grid(row=3, column=1)

tk.Button(info_frame, text="Calculate Total", bg="#6C3483", fg="white", command=calculate_total).grid(row=4, column=0, pady=5)
tk.Label(info_frame, text="Total:", bg="#ecf0f1").grid(row=4, column=1, sticky="w")
total_lbl = tk.Label(info_frame, text="0.00", width=10, bg="white")
total_lbl.grid(row=4, column=1, sticky="e")

tk.Button(root, text="Add to Cart", bg="green", fg="white", command=add_to_cart).pack(pady=10)

# ======= جدول المبيعات =======
cart_tree = ttk.Treeview(root, columns=("name", "qty", "unit", "total"), show="headings", height=7)
for i, col in enumerate(["Product", "Qty", "Unit Price", "Total"]):
    cart_tree.heading(i, text=col)
    cart_tree.column(i, width=150)
cart_tree.pack(pady=5)

# ======= الإجمالي =======
tk.Label(root, text="Total Invoice:", bg="#ecf0f1").pack()
invoice_total_lbl = tk.Label(root, text="0.00", font=("Helvetica", 14), bg="white", width=20)
invoice_total_lbl.pack()

tk.Button(root, text="Finalize Sale", bg="#e67e22", fg="white", width=20, command=finalize_sale).pack(pady=15)

root.mainloop()
