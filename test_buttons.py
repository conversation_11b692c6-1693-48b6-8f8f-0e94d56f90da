# -*- coding: utf-8 -*-
"""
اختبار الأزرار والاتصال بقاعدة البيانات
"""

import mysql.connector
from mysql.connector import Error

def connect_db():
    """الاتصال بقاعدة البيانات"""
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="pharmacy_db"
        )
    except Error as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔍 اختبار الاتصال بقاعدة البيانات...")
    
    db = connect_db()
    if db:
        print("✅ الاتصال بقاعدة البيانات ناجح")
        db.close()
        return True
    else:
        print("❌ فشل في الاتصال بقاعدة البيانات")
        return False

def test_tables():
    """اختبار وجود الجداول المطلوبة"""
    print("\n🔍 اختبار وجود الجداول...")
    
    required_tables = ['users', 'inventory', 'sales']
    
    try:
        db = connect_db()
        if not db:
            return False
            
        cursor = db.cursor()
        
        for table in required_tables:
            cursor.execute(f"SHOW TABLES LIKE '{table}'")
            result = cursor.fetchone()
            if result:
                print(f"✅ جدول {table} موجود")
                
                # اختبار عدد السجلات
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   📊 عدد السجلات: {count}")
            else:
                print(f"❌ جدول {table} غير موجود")
        
        cursor.close()
        db.close()
        return True
        
    except Error as e:
        print(f"❌ خطأ في اختبار الجداول: {e}")
        return False

def test_user_authentication():
    """اختبار تسجيل الدخول"""
    print("\n🔍 اختبار تسجيل الدخول...")
    
    test_users = [
        ('admin', 'admin123', 'admin'),
        ('user', 'user123', 'user')
    ]
    
    try:
        db = connect_db()
        if not db:
            return False
            
        cursor = db.cursor()
        
        for username, password, expected_role in test_users:
            cursor.execute("SELECT username, role FROM users WHERE username = %s AND password = %s", 
                          (username, password))
            result = cursor.fetchone()
            
            if result and result[1] == expected_role:
                print(f"✅ {username} - تسجيل الدخول ناجح ({expected_role})")
            else:
                print(f"❌ {username} - فشل في تسجيل الدخول")
        
        cursor.close()
        db.close()
        return True
        
    except Error as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

def test_inventory_operations():
    """اختبار عمليات المخزون"""
    print("\n🔍 اختبار عمليات المخزون...")
    
    try:
        db = connect_db()
        if not db:
            return False
            
        cursor = db.cursor()
        
        # اختبار إضافة منتج
        test_product = {
            'name': 'دواء اختبار',
            'category': 'أدوية',
            'wholesale_price': 10.0,
            'selling_price': 15.0,
            'quantity': 100,
            'expiry_date': '2025-12-01'
        }
        
        # حذف المنتج إذا كان موجوداً
        cursor.execute("DELETE FROM inventory WHERE product_name = %s", (test_product['name'],))
        
        # إضافة المنتج
        cursor.execute("""
            INSERT INTO inventory (product_name, category, wholesale_price, selling_price, quantity, expiry_date)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (test_product['name'], test_product['category'], test_product['wholesale_price'],
              test_product['selling_price'], test_product['quantity'], test_product['expiry_date']))
        
        # التحقق من الإضافة
        cursor.execute("SELECT * FROM inventory WHERE product_name = %s", (test_product['name'],))
        result = cursor.fetchone()
        
        if result:
            print("✅ إضافة منتج للمخزون - ناجح")
            
            # اختبار تحديث المنتج
            cursor.execute("UPDATE inventory SET quantity = %s WHERE product_name = %s", 
                          (150, test_product['name']))
            print("✅ تحديث منتج في المخزون - ناجح")
            
            # اختبار حذف المنتج
            cursor.execute("DELETE FROM inventory WHERE product_name = %s", (test_product['name'],))
            print("✅ حذف منتج من المخزون - ناجح")
        else:
            print("❌ فشل في إضافة منتج للمخزون")
        
        db.commit()
        cursor.close()
        db.close()
        return True
        
    except Error as e:
        print(f"❌ خطأ في اختبار المخزون: {e}")
        return False

def test_sales_operations():
    """اختبار عمليات المبيعات"""
    print("\n🔍 اختبار عمليات المبيعات...")
    
    try:
        db = connect_db()
        if not db:
            return False
            
        cursor = db.cursor()
        
        # إضافة منتج للاختبار
        cursor.execute("DELETE FROM inventory WHERE product_name = 'منتج اختبار مبيعات'")
        cursor.execute("""
            INSERT INTO inventory (product_name, category, wholesale_price, selling_price, quantity, expiry_date)
            VALUES ('منتج اختبار مبيعات', 'أدوية', 10.0, 15.0, 100, '2025-12-01')
        """)
        
        # اختبار إضافة مبيعة
        test_sale = {
            'invoice_id': 'TEST-001',
            'product_name': 'منتج اختبار مبيعات',
            'price': 15.0,
            'qty': 2,
            'total': 30.0,
            'payment_method': 'نقد',
            'customer': 'عميل اختبار',
            'phone': '123456789',
            'date': '2024-01-01',
            'time': '12:00:00'
        }
        
        cursor.execute("""
            INSERT INTO sales (invoice_id, product_name, price, qty, total, time, payment_method, customer, phone, date)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (test_sale['invoice_id'], test_sale['product_name'], test_sale['price'],
              test_sale['qty'], test_sale['total'], test_sale['time'], test_sale['payment_method'],
              test_sale['customer'], test_sale['phone'], test_sale['date']))
        
        # تحديث المخزون
        cursor.execute("UPDATE inventory SET quantity = quantity - %s WHERE product_name = %s",
                      (test_sale['qty'], test_sale['product_name']))
        
        # التحقق من المبيعة
        cursor.execute("SELECT * FROM sales WHERE invoice_id = %s", (test_sale['invoice_id'],))
        result = cursor.fetchone()
        
        if result:
            print("✅ إضافة مبيعة - ناجح")
            
            # التحقق من تحديث المخزون
            cursor.execute("SELECT quantity FROM inventory WHERE product_name = %s", 
                          (test_sale['product_name'],))
            new_qty = cursor.fetchone()[0]
            if new_qty == 98:  # 100 - 2
                print("✅ تحديث المخزون بعد المبيعة - ناجح")
            else:
                print("❌ فشل في تحديث المخزون بعد المبيعة")
        else:
            print("❌ فشل في إضافة مبيعة")
        
        # تنظيف البيانات
        cursor.execute("DELETE FROM sales WHERE invoice_id = %s", (test_sale['invoice_id'],))
        cursor.execute("DELETE FROM inventory WHERE product_name = %s", (test_sale['product_name'],))
        
        db.commit()
        cursor.close()
        db.close()
        return True
        
    except Error as e:
        print(f"❌ خطأ في اختبار المبيعات: {e}")
        return False

def test_modules_import():
    """اختبار استيراد الوحدات"""
    print("\n🔍 اختبار استيراد الوحدات...")
    
    modules_to_test = [
        ('inventory', 'صفحة المخزون'),
        ('sales', 'صفحة المبيعات'),
        ('users', 'صفحة المستخدمين'),
        ('invoice', 'صفحة الفواتير'),
        ('dashboard', 'لوحة التحكم'),
        ('theme', 'نظام الألوان'),
        ('user_management', 'إدارة المستخدمين')
    ]
    
    success_count = 0
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {description} ({module_name}) - تم الاستيراد بنجاح")
            success_count += 1
        except ImportError as e:
            print(f"❌ {description} ({module_name}) - فشل في الاستيراد: {e}")
        except Exception as e:
            print(f"⚠️ {description} ({module_name}) - خطأ: {e}")
    
    print(f"\n📊 نتيجة الاستيراد: {success_count}/{len(modules_to_test)} وحدة تم استيرادها بنجاح")
    return success_count == len(modules_to_test)

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل لأزرار النظام وقاعدة البيانات")
    print("=" * 60)
    
    tests = [
        ("الاتصال بقاعدة البيانات", test_database_connection),
        ("وجود الجداول", test_tables),
        ("تسجيل الدخول", test_user_authentication),
        ("عمليات المخزون", test_inventory_operations),
        ("عمليات المبيعات", test_sales_operations),
        ("استيراد الوحدات", test_modules_import)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_function():
                passed_tests += 1
                print(f"✅ {test_name} - نجح")
            else:
                print(f"❌ {test_name} - فشل")
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتيجة الاختبارات: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        print("\n🚀 يمكنك الآن تشغيل النظام:")
        print("   python main.py")
        print("\n📝 بيانات تسجيل الدخول:")
        print("   👑 المدير: admin / admin123")
        print("   👤 المستخدم: user / user123")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        print("\n💡 نصائح لحل المشاكل:")
        print("   1. تأكد من تشغيل MySQL Server")
        print("   2. شغل python setup.py لإعداد قاعدة البيانات")
        print("   3. شغل python fix_users.py لإصلاح جدول المستخدمين")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
