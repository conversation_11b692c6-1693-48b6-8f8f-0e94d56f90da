"""
ملف الألوان والتصميم الموحد للنظام
"""

# نظام الألوان الأساسي
COLORS = {
    # الألوان الأساسية
    'primary': '#2c3e50',      # أزرق داكن
    'secondary': '#34495e',    # رمادي داكن
    'accent': '#3498db',       # أزرق فاتح
    
    # ألوان الخلفية
    'bg_main': '#f8f9fa',      # خلفية رئيسية فاتحة
    'bg_card': '#ffffff',      # خلفية البطاقات
    'bg_sidebar': '#2c3e50',   # خلفية الشريط الجانبي
    
    # ألوان النصوص
    'text_primary': '#2c3e50',    # نص أساسي
    'text_secondary': '#34495e',  # نص ثانوي
    'text_light': '#7f8c8d',     # نص فاتح
    'text_white': '#ffffff',     # نص أبيض
    
    # ألوان الحالة
    'success': '#27ae60',      # أخضر للنجاح
    'warning': '#f39c12',      # برتقالي للتحذير
    'error': '#e74c3c',        # أحمر للخطأ
    'info': '#3498db',         # أزرق للمعلومات
    
    # ألوان إضافية
    'purple': '#9b59b6',       # بنفسجي
    'teal': '#1abc9c',         # تركوازي
    'gray': '#95a5a6',         # رمادي
    'dark_gray': '#7f8c8d',    # رمادي داكن
    
    # ألوان الأزرار
    'btn_primary': '#3498db',
    'btn_success': '#27ae60',
    'btn_warning': '#f39c12',
    'btn_danger': '#e74c3c',
    'btn_secondary': '#95a5a6',
    
    # ألوان الحدود
    'border_light': '#ecf0f1',
    'border_medium': '#bdc3c7',
    'border_dark': '#95a5a6',
}

# الخطوط
FONTS = {
    'main': ('Arial', 11),
    'heading': ('Arial', 14, 'bold'),
    'title': ('Arial', 18, 'bold'),
    'small': ('Arial', 9),
    'button': ('Arial', 11, 'bold'),
    'large': ('Arial', 16, 'bold'),
}

# أحجام وأبعاد
SIZES = {
    'padding_small': 5,
    'padding_medium': 10,
    'padding_large': 20,
    'button_width': 15,
    'button_height': 2,
    'entry_width': 25,
    'header_height': 60,
}

# أنماط الأزرار
BUTTON_STYLES = {
    'primary': {
        'bg': COLORS['btn_primary'],
        'fg': COLORS['text_white'],
        'font': FONTS['button'],
        'relief': 'flat',
        'cursor': 'hand2',
        'width': SIZES['button_width'],
        'height': SIZES['button_height']
    },
    'success': {
        'bg': COLORS['btn_success'],
        'fg': COLORS['text_white'],
        'font': FONTS['button'],
        'relief': 'flat',
        'cursor': 'hand2',
        'width': SIZES['button_width'],
        'height': SIZES['button_height']
    },
    'warning': {
        'bg': COLORS['btn_warning'],
        'fg': COLORS['text_white'],
        'font': FONTS['button'],
        'relief': 'flat',
        'cursor': 'hand2',
        'width': SIZES['button_width'],
        'height': SIZES['button_height']
    },
    'danger': {
        'bg': COLORS['btn_danger'],
        'fg': COLORS['text_white'],
        'font': FONTS['button'],
        'relief': 'flat',
        'cursor': 'hand2',
        'width': SIZES['button_width'],
        'height': SIZES['button_height']
    },
    'secondary': {
        'bg': COLORS['btn_secondary'],
        'fg': COLORS['text_white'],
        'font': FONTS['button'],
        'relief': 'flat',
        'cursor': 'hand2',
        'width': SIZES['button_width'],
        'height': SIZES['button_height']
    }
}

# أنماط الإطارات
FRAME_STYLES = {
    'main': {
        'bg': COLORS['bg_main']
    },
    'card': {
        'bg': COLORS['bg_card'],
        'relief': 'raised',
        'bd': 2
    },
    'header': {
        'bg': COLORS['primary'],
        'height': SIZES['header_height']
    },
    'sidebar': {
        'bg': COLORS['bg_sidebar'],
        'width': 200
    }
}

# أنماط التسميات
LABEL_STYLES = {
    'title': {
        'font': FONTS['title'],
        'bg': COLORS['primary'],
        'fg': COLORS['text_white']
    },
    'heading': {
        'font': FONTS['heading'],
        'bg': COLORS['bg_card'],
        'fg': COLORS['text_primary']
    },
    'normal': {
        'font': FONTS['main'],
        'bg': COLORS['bg_card'],
        'fg': COLORS['text_secondary']
    },
    'small': {
        'font': FONTS['small'],
        'bg': COLORS['bg_card'],
        'fg': COLORS['text_light']
    }
}

# أنماط حقول الإدخال
ENTRY_STYLES = {
    'normal': {
        'font': FONTS['main'],
        'relief': 'solid',
        'bd': 1,
        'width': SIZES['entry_width']
    }
}

def apply_button_style(button, style_name='primary'):
    """تطبيق نمط على زر"""
    if style_name in BUTTON_STYLES:
        style = BUTTON_STYLES[style_name]
        button.configure(**style)

def apply_frame_style(frame, style_name='main'):
    """تطبيق نمط على إطار"""
    if style_name in FRAME_STYLES:
        style = FRAME_STYLES[style_name]
        frame.configure(**style)

def apply_label_style(label, style_name='normal'):
    """تطبيق نمط على تسمية"""
    if style_name in LABEL_STYLES:
        style = LABEL_STYLES[style_name]
        label.configure(**style)

def apply_entry_style(entry, style_name='normal'):
    """تطبيق نمط على حقل إدخال"""
    if style_name in ENTRY_STYLES:
        style = ENTRY_STYLES[style_name]
        entry.configure(**style)

def get_color(color_name):
    """الحصول على لون من نظام الألوان"""
    return COLORS.get(color_name, '#000000')

def get_font(font_name):
    """الحصول على خط من نظام الخطوط"""
    return FONTS.get(font_name, ('Arial', 11))

# أيقونات نصية للاستخدام في الواجهة - محدثة لصيدلية الشفاء
ICONS = {
    'add': '➕',
    'edit': '✏️',
    'delete': '🗑️',
    'save': '💾',
    'print': '🖨️',
    'search': '🔍',
    'refresh': '🔄',
    'clear': '🧹',
    'inventory': '📦',
    'sales': '🛒',
    'invoice': '🧾',
    'user': '👤',
    'dashboard': '📊',
    'phone': '📞',
    'money': '💰',
    'calendar': '📅',
    'check': '✅',
    'error': '❌',
    'warning': '⚠️',
    'info': 'ℹ️',
    'cart': '🛍️',
    'customer': '👥',
    'product': '📋',
    'stats': '📈',
    'settings': '⚙️',
    'logout': '🚪',
    'close': '✖',
    'pharmacy': '🏥',
    'medical': '⚕️',
    'pills': '💊',
    'health': '🩺',
    'cross': '✚',
    'heart': '❤️',
    'shifa': '🏥'  # شعار صيدلية الشفاء
}

# معلومات الهوية البصرية لصيدلية الشفاء
PHARMACY_BRANDING = {
    'name_ar': '🏥 صيدلية الشفاء',
    'name_en': 'Al-Shifa Pharmacy',
    'slogan_ar': 'نظام إدارة شامل ومتطور',
    'slogan_en': 'Comprehensive Management System',
    'logo': '🏥',
    'colors': {
        'primary': '#27ae60',    # أخضر طبي
        'secondary': '#2ecc71',  # أخضر فاتح
        'accent': '#3498db'      # أزرق
    }
}
