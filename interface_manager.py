"""
مدير الواجهات - نظام تحديث شامل لجميع الواجهات
"""

import tkinter as tk
from tkinter import ttk
import mysql.connector
from datetime import datetime

class InterfaceManager:
    """مدير تحديث جميع الواجهات في النظام"""
    
    def __init__(self):
        self.registered_interfaces = {}
        self.update_callbacks = {}
    
    def register_interface(self, interface_name, interface_widget, update_callback=None):
        """تسجيل واجهة للتحديث التلقائي"""
        self.registered_interfaces[interface_name] = interface_widget
        if update_callback:
            self.update_callbacks[interface_name] = update_callback
    
    def unregister_interface(self, interface_name):
        """إلغاء تسجيل واجهة"""
        if interface_name in self.registered_interfaces:
            del self.registered_interfaces[interface_name]
        if interface_name in self.update_callbacks:
            del self.update_callbacks[interface_name]
    
    def update_all_interfaces(self):
        """تحديث جميع الواجهات المسجلة"""
        try:
            for interface_name, callback in self.update_callbacks.items():
                if interface_name in self.registered_interfaces:
                    widget = self.registered_interfaces[interface_name]
                    if widget.winfo_exists():
                        callback()
            print("✅ تم تحديث جميع الواجهات")
        except Exception as e:
            print(f"❌ خطأ في تحديث الواجهات: {e}")
    
    def update_specific_interface(self, interface_name):
        """تحديث واجهة محددة"""
        try:
            if interface_name in self.update_callbacks:
                if interface_name in self.registered_interfaces:
                    widget = self.registered_interfaces[interface_name]
                    if widget.winfo_exists():
                        self.update_callbacks[interface_name]()
                        print(f"✅ تم تحديث واجهة {interface_name}")
        except Exception as e:
            print(f"❌ خطأ في تحديث واجهة {interface_name}: {e}")

# إنشاء مثيل عام لمدير الواجهات
interface_manager = InterfaceManager()

def connect_db():
    """الاتصال بقاعدة البيانات"""
    return mysql.connector.connect(
        host="localhost",
        user="root",
        password="",
        database="pharmacy_db"
    )

def update_inventory_data():
    """تحديث بيانات المخزون"""
    try:
        # البحث عن جدول المخزون في الواجهات المسجلة
        if 'inventory' in interface_manager.registered_interfaces:
            inventory_widget = interface_manager.registered_interfaces['inventory']
            
            # البحث عن Treeview في واجهة المخزون
            def find_treeview(widget):
                if isinstance(widget, ttk.Treeview):
                    return widget
                for child in widget.winfo_children():
                    result = find_treeview(child)
                    if result:
                        return result
                return None
            
            tree = find_treeview(inventory_widget)
            if tree:
                # مسح البيانات الحالية
                for item in tree.get_children():
                    tree.delete(item)
                
                # إعادة تحميل البيانات
                db = connect_db()
                cursor = db.cursor()
                cursor.execute("SELECT product_name, category, wholesale_price, selling_price, quantity, expiry_date FROM inventory ORDER BY product_name")
                for row in cursor.fetchall():
                    tree.insert("", "end", values=row)
                cursor.close()
                db.close()
                
                print("✅ تم تحديث بيانات المخزون")
    except Exception as e:
        print(f"❌ خطأ في تحديث بيانات المخزون: {e}")

def update_invoices_data():
    """تحديث بيانات الفواتير"""
    try:
        # البحث عن جدول الفواتير في الواجهات المسجلة
        if 'invoices' in interface_manager.registered_interfaces:
            invoices_widget = interface_manager.registered_interfaces['invoices']
            
            # البحث عن Treeview في واجهة الفواتير
            def find_invoice_treeview(widget):
                if isinstance(widget, ttk.Treeview) and len(widget['columns']) >= 6:
                    return widget
                for child in widget.winfo_children():
                    result = find_invoice_treeview(child)
                    if result:
                        return result
                return None
            
            tree = find_invoice_treeview(invoices_widget)
            if tree:
                # مسح البيانات الحالية
                for item in tree.get_children():
                    tree.delete(item)
                
                # إعادة تحميل البيانات
                db = connect_db()
                cursor = db.cursor()
                cursor.execute("""SELECT DISTINCT invoice_id, customer, phone, 
                                 SUM(total) as total_amount, payment_method, date, time 
                                 FROM sales WHERE invoice_id IS NOT NULL
                                 GROUP BY invoice_id, customer, phone, payment_method, date, time 
                                 ORDER BY date DESC, time DESC""")
                for invoice in cursor.fetchall():
                    tree.insert("", "end", values=(
                        invoice[0],  # invoice_id
                        invoice[1],  # customer
                        invoice[2] if invoice[2] else "غير محدد",  # phone
                        f"Ghc {invoice[3]:.2f}",  # total
                        invoice[4],  # payment_method
                        invoice[5],  # date
                        invoice[6]   # time
                    ))
                cursor.close()
                db.close()
                
                print("✅ تم تحديث بيانات الفواتير")
    except Exception as e:
        print(f"❌ خطأ في تحديث بيانات الفواتير: {e}")

def update_dashboard_data():
    """تحديث بيانات لوحة التحكم باستخدام النظام الجديد"""
    try:
        # استيراد دالة التحديث من dashboard.py
        from dashboard import refresh_dashboard_now, dashboard_frame_ref

        if 'dashboard' in interface_manager.registered_interfaces:
            dashboard_widget = interface_manager.registered_interfaces['dashboard']

            # استخدام دالة التحديث المحسنة
            refresh_dashboard_now(dashboard_widget)
            print("✅ تم تحديث بيانات لوحة التحكم")

    except Exception as e:
        print(f"❌ خطأ في تحديث بيانات لوحة التحكم: {e}")

def trigger_global_update():
    """تشغيل تحديث شامل لجميع الواجهات"""
    try:
        print("🔄 بدء التحديث الشامل...")
        
        # تحديث بيانات المخزون
        update_inventory_data()
        
        # تحديث بيانات الفواتير
        update_invoices_data()
        
        # تحديث بيانات لوحة التحكم
        update_dashboard_data()
        
        # تحديث جميع الواجهات المسجلة
        interface_manager.update_all_interfaces()
        
        print("✅ تم التحديث الشامل بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في التحديث الشامل: {e}")

# دوال مساعدة للتسجيل السريع
def register_inventory_interface(widget):
    """تسجيل واجهة المخزون"""
    interface_manager.register_interface('inventory', widget, update_inventory_data)

def register_invoices_interface(widget):
    """تسجيل واجهة الفواتير"""
    interface_manager.register_interface('invoices', widget, update_invoices_data)

def register_dashboard_interface(widget):
    """تسجيل لوحة التحكم"""
    interface_manager.register_interface('dashboard', widget, update_dashboard_data)
