# -*- coding: utf-8 -*-
import tkinter as tk
from tkinter import ttk, messagebox
import mysql.connector
from datetime import datetime
import os
import subprocess
import platform
import sys
from validation import validate_sales_data
from interface_manager import trigger_global_update, interface_manager
from payment_methods import get_payment_methods_list, clean_payment_method

# مكتبات HTML والتصميم
try:
    import webbrowser
    import tempfile
    HTML_AVAILABLE = True
except ImportError:
    HTML_AVAILABLE = False
    print("⚠️ مكتبة HTML غير متوفرة.")

# إعداد الترميز للنصوص العربية
if sys.platform.startswith('win'):
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'Arabic_Saudi Arabia.1256')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'ar_SA.UTF-8')
        except:
            pass

def connect_db():
    return mysql.connector.connect(
        host="localhost",
        user="root",
        password="",
        database="pharmacy_db"
    )



def create_html_invoice(invoice_id, cart_items, customer_name, phone, payment_method, invoice_date):
    """إنشاء فاتورة HTML جذابة ومهنية"""
    try:
        # إنشاء اسم ملف آمن
        safe_invoice_id = str(invoice_id).replace('/', '_').replace('\\', '_').replace(':', '_')
        filename = f"invoice_{safe_invoice_id}.html"

        # حساب الإجمالي
        total_amount = sum(float(item[3]) for item in cart_items)

        # إنشاء محتوى HTML
        html_content = f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم {invoice_id}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }}

        .invoice-container {{
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            animation: slideIn 0.6s ease-out;
        }}

        @keyframes slideIn {{
            from {{
                opacity: 0;
                transform: translateY(30px);
            }}
            to {{
                opacity: 1;
                transform: translateY(0);
            }}
        }}

        .header {{
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }}

        .header::before {{
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }}

        @keyframes float {{
            0% {{ transform: translate(-50%, -50%) rotate(0deg); }}
            100% {{ transform: translate(-50%, -50%) rotate(360deg); }}
        }}

        .header h1 {{
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }}

        .header p {{
            font-size: 1.2em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }}

        .invoice-info {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
            background: #f8f9fa;
        }}

        .info-section {{
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-right: 4px solid #27ae60;
        }}

        .info-section h3 {{
            color: #2c3e50;
            font-size: 1.3em;
            margin-bottom: 15px;
            font-weight: 600;
        }}

        .info-item {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }}

        .info-item:last-child {{
            border-bottom: none;
        }}

        .info-label {{
            font-weight: 600;
            color: #34495e;
        }}

        .info-value {{
            color: #2c3e50;
        }}

        .products-section {{
            padding: 30px;
        }}

        .products-title {{
            text-align: center;
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 30px;
            font-weight: 600;
        }}

        .products-table {{
            width: 100%;
            border-collapse: collapse;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }}

        .products-table th {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 15px;
            text-align: center;
            font-weight: 600;
            font-size: 1.1em;
        }}

        .products-table td {{
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
            transition: background-color 0.3s ease;
        }}

        .products-table tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}

        .products-table tr:hover {{
            background-color: #e8f5e8;
        }}

        .total-row {{
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%) !important;
            color: white !important;
            font-weight: 700;
            font-size: 1.2em;
        }}

        .total-row td {{
            border-bottom: none !important;
        }}

        .footer {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }}

        .footer h3 {{
            font-size: 1.5em;
            margin-bottom: 10px;
            font-weight: 600;
        }}

        .footer p {{
            opacity: 0.9;
            margin-bottom: 20px;
        }}

        .print-date {{
            font-size: 0.9em;
            opacity: 0.7;
            margin-top: 20px;
        }}

        @media print {{
            body {{
                background: white;
                padding: 0;
            }}

            .invoice-container {{
                box-shadow: none;
                border-radius: 0;
            }}
        }}

        .print-button {{
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 10px 25px rgba(39, 174, 96, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }}

        .print-button:hover {{
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(39, 174, 96, 0.4);
        }}

        @media print {{
            .print-button {{
                display: none;
            }}
        }}
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="header">
            <h1>🏥 صيدلية الشفاء</h1>
            <p>Al-Shifa Pharmacy - نظام إدارة شامل ومتطور</p>
        </div>

        <div class="invoice-info">
            <div class="info-section">
                <h3>📋 معلومات الفاتورة</h3>
                <div class="info-item">
                    <span class="info-label">رقم الفاتورة:</span>
                    <span class="info-value">{invoice_id}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">التاريخ:</span>
                    <span class="info-value">{invoice_date.strftime('%Y-%m-%d')}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الوقت:</span>
                    <span class="info-value">{invoice_date.strftime('%H:%M:%S')}</span>
                </div>
            </div>

            <div class="info-section">
                <h3>👤 بيانات العميل</h3>
                <div class="info-item">
                    <span class="info-label">اسم العميل:</span>
                    <span class="info-value">{customer_name}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">رقم الهاتف:</span>
                    <span class="info-value">{phone if phone else 'غير محدد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">طريقة الدفع:</span>
                    <span class="info-value">{payment_method}</span>
                </div>
            </div>
        </div>

        <div class="products-section">
            <h2 class="products-title">🛒 تفاصيل المشتريات</h2>
            <table class="products-table">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>السعر</th>
                        <th>الكمية</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>"""

        # إضافة المنتجات
        for item in cart_items:
            product_name = str(item[0])
            price = f"Ghc {float(item[1]):.2f}"
            quantity = str(int(item[2]))
            item_total = f"Ghc {float(item[3]):.2f}"

            html_content += f"""
                    <tr>
                        <td>{product_name}</td>
                        <td>{price}</td>
                        <td>{quantity}</td>
                        <td>{item_total}</td>
                    </tr>"""

        # إضافة الإجمالي
        html_content += f"""
                    <tr class="total-row">
                        <td colspan="3">الإجمالي النهائي</td>
                        <td>Ghc {total_amount:.2f}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="footer">
            <h3>🙏 شكراً لتعاملكم معنا</h3>
            <p>نتمنى لكم دوام الصحة والعافية</p>
            <div class="print-date">
                تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            </div>
        </div>
    </div>

    <button class="print-button" onclick="window.print()">🖨️ طباعة</button>

    <script>
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {{
            // تأثير hover للجدول
            const rows = document.querySelectorAll('.products-table tr:not(.total-row)');
            rows.forEach(row => {{
                row.addEventListener('mouseenter', function() {{
                    this.style.transform = 'scale(1.02)';
                    this.style.transition = 'all 0.3s ease';
                }});
                row.addEventListener('mouseleave', function() {{
                    this.style.transform = 'scale(1)';
                }});
            }});

            // طباعة تلقائية بعد التحميل (اختياري)
            // setTimeout(() => window.print(), 1000);
        }});
    </script>
</body>
</html>"""

        # حفظ الملف
        with open(filename, 'w', encoding='utf-8', errors='replace') as f:
            f.write(html_content)

        # فتح الفاتورة في المتصفح
        try:
            if platform.system() == "Windows":
                os.startfile(filename)
            elif platform.system() == "Darwin":  # macOS
                subprocess.call(["open", filename])
            else:  # Linux
                subprocess.call(["xdg-open", filename])

            print(f"✅ تم إنشاء فاتورة HTML جذابة: {filename}")
            return True

        except Exception as e:
            print(f"خطأ في فتح HTML: {e}")
            # محاولة فتح بالمتصفح الافتراضي
            try:
                webbrowser.open(f'file://{os.path.abspath(filename)}')
                print(f"✅ تم فتح الفاتورة في المتصفح: {filename}")
                return True
            except Exception as e2:
                print(f"خطأ في فتح المتصفح: {e2}")
                messagebox.showinfo("تم الحفظ", f"تم حفظ الفاتورة HTML في: {filename}")
                return True

    except Exception as e:
        print(f"❌ خطأ في إنشاء HTML: {e}")
        # العودة للطريقة النصية في حالة الخطأ
        return print_invoice_text(invoice_id, cart_items, customer_name, phone, payment_method, invoice_date)

def print_invoice_text(invoice_id, cart_items, customer_name, phone, payment_method, invoice_date):
    """طباعة الفاتورة في ملف نصي (نسخة احتياطية)"""
    try:
        # إنشاء محتوى الفاتورة
        invoice_content = generate_invoice_content(invoice_id, cart_items, customer_name, phone, payment_method, invoice_date)

        # إنشاء اسم ملف آمن
        safe_invoice_id = str(invoice_id).replace('/', '_').replace('\\', '_').replace(':', '_')
        filename = f"invoice_{safe_invoice_id}.txt"

        # محاولة حفظ الفاتورة بترميزات مختلفة
        saved = False
        encodings_to_try = ['utf-8-sig', 'utf-8', 'cp1256', 'latin-1']

        for encoding in encodings_to_try:
            try:
                with open(filename, 'w', encoding=encoding, errors='replace') as f:
                    f.write(invoice_content)
                saved = True
                print(f"✅ تم حفظ الفاتورة النصية بترميز: {encoding}")
                break
            except UnicodeEncodeError:
                continue
            except Exception as e:
                print(f"خطأ في الحفظ بترميز {encoding}: {e}")
                continue

        if saved:
            # فتح الفاتورة للطباعة
            try:
                if platform.system() == "Windows":
                    os.startfile(filename)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.call(["open", filename])
                else:  # Linux
                    subprocess.call(["xdg-open", filename])

                print(f"✅ تم فتح الفاتورة النصية: {filename}")
                return True
            except Exception as e:
                print(f"خطأ في فتح الفاتورة: {e}")
                messagebox.showinfo("تم الحفظ", f"تم حفظ الفاتورة في: {filename}")
                return True

        return False

    except Exception as e:
        error_msg = str(e).encode('utf-8', errors='ignore').decode('utf-8')
        print(f"❌ خطأ في طباعة الفاتورة النصية: {error_msg}")
        messagebox.showerror("خطأ في الطباعة", f"حدث خطأ أثناء طباعة الفاتورة:\n{error_msg}")
        return False

def print_invoice(invoice_id, cart_items, customer_name, phone, payment_method, invoice_date):
    """دالة طباعة الفاتورة الرئيسية - تحاول HTML أولاً ثم النص"""
    if HTML_AVAILABLE:
        return create_html_invoice(invoice_id, cart_items, customer_name, phone, payment_method, invoice_date)
    else:
        return print_invoice_text(invoice_id, cart_items, customer_name, phone, payment_method, invoice_date)

def generate_invoice_content(invoice_id, cart_items, customer_name, phone, payment_method, invoice_date):
    """إنشاء محتوى الفاتورة مع دعم الترميز العربي"""

    try:
        # حساب الإجمالي
        total_amount = sum(item[3] for item in cart_items)

        # تنظيف النصوص من الأحرف الخاصة
        def clean_text(text):
            if text is None:
                return "غير محدد"
            try:
                return str(text).encode('utf-8', errors='ignore').decode('utf-8')
            except:
                return str(text)

        # تنظيف البيانات
        clean_invoice_id = clean_text(invoice_id)
        clean_customer = clean_text(customer_name)
        clean_phone = clean_text(phone) if phone else "غير محدد"
        clean_payment = clean_text(payment_method)

        # إنشاء محتوى الفاتورة بطريقة آمنة
        content_lines = [
            "=" * 60,
            "                    🏥 صيدلية الشفاء".center(60),
            "                   Al-Shifa Pharmacy".center(60),
            "                 نظام إدارة شامل ومتطور".center(60),
            "=" * 60,
            "",
            f"رقم الفاتورة: {clean_invoice_id}",
            f"التاريخ: {invoice_date.strftime('%Y-%m-%d')}",
            f"الوقت: {invoice_date.strftime('%H:%M:%S')}",
            "",
            "=" * 60,
            "                    بيانات العميل".center(60),
            "=" * 60,
            "",
            f"اسم العميل: {clean_customer}",
            f"رقم الهاتف: {clean_phone}",
            f"طريقة الدفع: {clean_payment}",
            "",
            "=" * 60,
            "                    تفاصيل المشتريات".center(60),
            "=" * 60,
            "",
            f"{'المنتج':<25} {'السعر':<10} {'الكمية':<8} {'الإجمالي':<12}",
            "-" * 60
        ]

        # إضافة تفاصيل المنتجات
        for item in cart_items:
            try:
                product_name = clean_text(item[0])[:24]  # قطع الاسم إذا كان طويلاً
                price = f"Ghc {float(item[1]):.2f}"
                quantity = str(int(item[2]))
                item_total = f"Ghc {float(item[3]):.2f}"

                content_lines.append(f"{product_name:<25} {price:<10} {quantity:<8} {item_total:<12}")
            except Exception as e:
                print(f"خطأ في معالجة المنتج: {e}")
                continue

        # إضافة الإجمالي والخاتمة
        content_lines.extend([
            "-" * 60,
            f"{'الإجمالي النهائي:':<45} Ghc {total_amount:.2f}",
            "=" * 60,
            "",
            "                    شكراً لتعاملكم معنا".center(60),
            "                   نتمنى لكم دوام الصحة والعافية".center(60),
            "",
            "=" * 60,
            "",
            f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            ""
        ])

        # دمج الأسطر
        content = "\n".join(content_lines)
        return content

    except Exception as e:
        print(f"خطأ في إنشاء محتوى الفاتورة: {e}")
        # إنشاء فاتورة بسيطة في حالة الخطأ
        simple_content = f"""
صيدلية الشفاء
رقم الفاتورة: {invoice_id}
التاريخ: {invoice_date.strftime('%Y-%m-%d %H:%M:%S')}
العميل: {customer_name}
الإجمالي: Ghc {sum(item[3] for item in cart_items):.2f}
شكراً لزيارتكم
"""
        return simple_content

def sales_page(root):
    frame = tk.Frame(root, bg="#f8f9fa")

    # إطار العنوان مع تصميم أنيق
    header_frame = tk.Frame(frame, bg="#2c3e50", height=60)
    header_frame.pack(fill="x", pady=(0, 20))
    header_frame.pack_propagate(False)

    tk.Label(header_frame, text="🛒 نقطة البيع وإنشاء الفواتير", font=("Arial", 18, "bold"),
             bg="#2c3e50", fg="white").pack(expand=True)

    # إطار النموذج مع تصميم محسن
    form_frame = tk.Frame(frame, bg="#ffffff", relief="raised", bd=2)
    form_frame.pack(pady=10, padx=20, fill="x")

    tk.Label(form_frame, text="📝 بيانات المنتج والعميل", font=("Arial", 14, "bold"),
             bg="#ffffff", fg="#2c3e50").grid(row=0, column=0, columnspan=4, pady=15)

    def load_product_names():
        db = connect_db()
        cursor = db.cursor()
        cursor.execute("SELECT product_name FROM inventory")
        names = [row[0] for row in cursor.fetchall()]
        cursor.close()
        db.close()
        return names

    # الصف الأول - المنتج والسعر
    tk.Label(form_frame, text="المنتج:", font=("Arial", 11, "bold"),
             bg="#ffffff", fg="#34495e").grid(row=1, column=0, sticky="e", padx=10, pady=8)
    product_names = load_product_names()
    product_combo = ttk.Combobox(form_frame, values=product_names, width=25, font=("Arial", 10))
    product_combo.grid(row=1, column=1, padx=10, pady=8)
    product_combo.set("اختر المنتج")

    tk.Label(form_frame, text="السعر:", font=("Arial", 11, "bold"),
             bg="#ffffff", fg="#34495e").grid(row=1, column=2, sticky="e", padx=10, pady=8)
    price_entry = tk.Entry(form_frame, width=15, font=("Arial", 10), relief="solid", bd=1)
    price_entry.grid(row=1, column=3, padx=10, pady=8)

    def on_product_select(event):
        selected_product = product_combo.get()
        if not selected_product or selected_product == "اختر المنتج":
            return
        try:
            db = connect_db()
            cursor = db.cursor()
            cursor.execute("SELECT selling_price, quantity FROM inventory WHERE product_name = %s", (selected_product,))
            result = cursor.fetchone()
            if result:
                price_entry.delete(0, tk.END)
                price_entry.insert(0, str(result[0]))
                # عرض الكمية المتاحة
                available_qty = result[1]
                available_qty_label.config(text=f"متاح: {available_qty}")
                available_qty_label.update()
                qty_entry.delete(0, tk.END)
                qty_entry.insert(0, "1")
                # تحديث فوري للواجهة
                force_ui_update()

                # إضافة تلقائية للسلة بعد ثانية واحدة (اختياري)
                # root.after(1000, auto_add_to_cart)

            cursor.close()
            db.close()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في جلب بيانات المنتج: {str(e)}")

    def auto_add_to_cart():
        """إضافة تلقائية للسلة عند اختيار المنتج"""
        if product_combo.get() and product_combo.get() != "اختر المنتج" and qty_entry.get():
            add_to_cart()

    product_combo.bind("<<ComboboxSelected>>", on_product_select)

    # الصف الثاني - الكمية وطريقة الدفع
    tk.Label(form_frame, text="الكمية:", font=("Arial", 11, "bold"),
             bg="#ffffff", fg="#34495e").grid(row=2, column=0, sticky="e", padx=10, pady=8)
    qty_entry = tk.Entry(form_frame, width=25, font=("Arial", 10), relief="solid", bd=1)
    qty_entry.grid(row=2, column=1, padx=10, pady=8)

    # تسمية لعرض الكمية المتاحة
    available_qty_label = tk.Label(form_frame, text="", font=("Arial", 9),
                                  bg="#ffffff", fg="#27ae60")
    available_qty_label.grid(row=2, column=1, sticky="e", padx=10, pady=(35, 0))

    # ربط Enter بإضافة للسلة
    def on_qty_enter(event):
        if product_combo.get() and product_combo.get() != "اختر المنتج":
            add_to_cart()

    qty_entry.bind("<Return>", on_qty_enter)

    tk.Label(form_frame, text="طريقة الدفع:", font=("Arial", 11, "bold"),
             bg="#ffffff", fg="#34495e").grid(row=2, column=2, sticky="e", padx=10, pady=8)

    # طرق الدفع المحدثة للمحافظ الإلكترونية الموريتانية
    payment_methods = get_payment_methods_list()

    method_combo = ttk.Combobox(form_frame, values=payment_methods,
                               width=20, font=("Arial", 10), state="readonly")
    method_combo.grid(row=2, column=3, padx=10, pady=8)
    method_combo.set("💰 نقدي")

    # الصف الثالث - بيانات العميل
    tk.Label(form_frame, text="اسم العميل:", font=("Arial", 11, "bold"),
             bg="#ffffff", fg="#34495e").grid(row=3, column=0, sticky="e", padx=10, pady=8)
    customer_entry = tk.Entry(form_frame, width=25, font=("Arial", 10), relief="solid", bd=1)
    customer_entry.grid(row=3, column=1, padx=10, pady=8)
    customer_entry.insert(0, "عميل")  # قيمة افتراضية

    tk.Label(form_frame, text="رقم الهاتف:", font=("Arial", 11, "bold"),
             bg="#ffffff", fg="#34495e").grid(row=3, column=2, sticky="e", padx=10, pady=8)
    phone_entry = tk.Entry(form_frame, width=15, font=("Arial", 10), relief="solid", bd=1)
    phone_entry.grid(row=3, column=3, padx=10, pady=8)

    # الصف الرابع - أزرار العمليات
    actions_frame = tk.Frame(form_frame, bg="#ffffff")
    actions_frame.grid(row=4, column=0, columnspan=4, pady=15)

    tk.Button(actions_frame, text="⚡ إضافة سريعة للسلة", command=lambda: add_to_cart(),
             bg="#27ae60", fg="white", font=("Arial", 11, "bold"),
             width=18, height=1, relief="flat", cursor="hand2").pack(side="left", padx=5)

    tk.Button(actions_frame, text="🧾 إنهاء البيع وطباعة الفاتورة", command=lambda: complete_sale(),
             bg="#e74c3c", fg="white", font=("Arial", 11, "bold"),
             width=25, height=1, relief="flat", cursor="hand2").pack(side="left", padx=5)

    # إطار السلة مع تصميم محسن
    cart_frame = tk.Frame(frame, bg="#ffffff", relief="raised", bd=2)
    cart_frame.pack(pady=20, padx=20, fill="both", expand=True)

    tk.Label(cart_frame, text="🛍️ سلة التسوق", font=("Arial", 14, "bold"),
             bg="#ffffff", fg="#2c3e50").pack(pady=10)

    # تحسين تصميم جدول السلة
    style = ttk.Style()
    style.configure("Cart.Treeview.Heading", font=("Arial", 11, "bold"))
    style.configure("Cart.Treeview", font=("Arial", 10))

    cart = []
    cart_tree = ttk.Treeview(cart_frame, columns=("product", "price", "qty", "total"),
                            show="headings", height=10, style="Cart.Treeview")

    headers = ["المنتج", "السعر", "الكمية", "الإجمالي"]
    for i, col in enumerate(["product", "price", "qty", "total"]):
        cart_tree.heading(col, text=headers[i])
        cart_tree.column(col, width=150, anchor="center")

    # إضافة شريط التمرير للسلة
    cart_scrollbar = ttk.Scrollbar(cart_frame, orient="vertical", command=cart_tree.yview)
    cart_tree.configure(yscrollcommand=cart_scrollbar.set)

    cart_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=(0, 10))
    cart_scrollbar.pack(side="right", fill="y", pady=(0, 10))

    # إطار الإجمالي مع تصميم أنيق
    total_frame = tk.Frame(frame, bg="#ffffff", relief="raised", bd=2)
    total_frame.pack(pady=10, padx=20, fill="x")

    total_var = tk.StringVar(value="0.00")
    tk.Label(total_frame, text="💰 الإجمالي:", font=("Arial", 16, "bold"),
             bg="#ffffff", fg="#2c3e50").pack(side="left", padx=20, pady=15)
    tk.Label(total_frame, textvariable=total_var, font=("Arial", 18, "bold"),
             fg="#27ae60", bg="#ffffff").pack(side="left", padx=10, pady=15)

    # إطار التلميحات والحالة
    tips_frame = tk.Frame(frame, bg="#ecf0f1", relief="solid", bd=1)
    tips_frame.pack(pady=10, padx=20, fill="x")

    tips_text = "💡 نصائح سريعة: اختر المنتج → أدخل الكمية → Enter للإضافة | F4 للبيع السريع | Ctrl+Enter لإنهاء البيع"
    tips_label = tk.Label(tips_frame, text=tips_text, font=("Arial", 9),
                         bg="#ecf0f1", fg="#7f8c8d", wraplength=800)
    tips_label.pack(pady=8, padx=10)

    # إطار حالة النظام
    status_frame = tk.Frame(frame, bg="#ffffff", relief="raised", bd=1)
    status_frame.pack(pady=5, padx=20, fill="x")

    status_var = tk.StringVar(value="🟢 النظام جاهز للبيع")
    status_label = tk.Label(status_frame, textvariable=status_var, font=("Arial", 10, "bold"),
                           bg="#ffffff", fg="#27ae60")
    status_label.pack(pady=5)

    def update_status(message, color="#27ae60"):
        """تحديث حالة النظام"""
        status_var.set(message)
        status_label.config(fg=color)
        # تحديث فوري للواجهة
        status_label.update()
        root.update_idletasks()
        # إعادة تعيين الحالة بعد 3 ثوانٍ
        root.after(3000, lambda: status_var.set("🟢 النظام جاهز للبيع") or status_label.config(fg="#27ae60"))

    def force_ui_update():
        """فرض تحديث فوري للواجهة"""
        try:
            root.update_idletasks()
            root.update()
            # تحديث جميع العناصر المرئية
            product_combo.update()
            if hasattr(available_qty_label, 'update'):
                available_qty_label.update()
            cart_tree.update()
            status_label.update()
        except Exception as e:
            print(f"خطأ في تحديث الواجهة: {e}")

    # تم نقل دوال التحديث إلى interface_manager.py

    def add_to_cart():
        # التحقق من صحة البيانات الأساسية
        valid, errors = validate_sales_data(
            product_combo.get(),
            price_entry.get(),
            qty_entry.get(),
            "عميل مؤقت",  # سيتم التحقق من اسم العميل عند إنشاء الفاتورة
            phone_entry.get()
        )

        # إزالة خطأ اسم العميل من القائمة لأنه سيتم التحقق منه لاحقاً
        errors = [error for error in errors if "اسم العميل" not in error]

        if not valid and errors:
            error_message = "يرجى تصحيح الأخطاء التالية:\n\n" + "\n".join(f"• {error}" for error in errors)
            messagebox.showerror("❌ خطأ في البيانات", error_message)
            return

        name = product_combo.get()
        try:
            price = float(price_entry.get().strip())
            qty = int(qty_entry.get().strip())

            # التحقق من توفر الكمية في المخزون
            db = connect_db()
            cursor = db.cursor()
            cursor.execute("SELECT quantity FROM inventory WHERE product_name = %s", (name,))
            result = cursor.fetchone()
            cursor.close()
            db.close()

            if not result:
                messagebox.showerror("❌ خطأ", "المنتج غير موجود في المخزون")
                return

            available_qty = result[0]

            # التحقق من الكمية المطلوبة مع ما هو موجود في السلة
            cart_qty = sum(item[2] for item in cart if item[0] == name)
            total_requested = qty + cart_qty

            if total_requested > available_qty:
                messagebox.showerror("❌ خطأ",
                    f"الكمية الإجمالية المطلوبة ({total_requested}) أكبر من المتوفر ({available_qty})\n"
                    f"الكمية في السلة حالياً: {cart_qty}")
                return

            total = price * qty
            cart.append((name, price, qty, total))
            cart_tree.insert("", "end", values=(name, f"Ghc {price:.2f}", qty, f"Ghc {total:.2f}"))
            update_total()

            # رسالة نجاح مع تفاصيل
            success_msg = f"🛒 تم إضافة المنتج بنجاح!\n\n"
            success_msg += f"📦 المنتج: {name}\n"
            success_msg += f"💰 السعر: Ghc {price:.2f}\n"
            success_msg += f"🔢 الكمية: {qty}\n"
            success_msg += f"💵 الإجمالي: Ghc {total:.2f}\n\n"
            success_msg += f"🛍️ عدد المنتجات في السلة: {len(cart)}"

            messagebox.showinfo("✅ تمت الإضافة", success_msg)

            # تحديث حالة النظام
            update_status(f"✅ تم إضافة {name} للسلة", "#27ae60")

            # مسح الحقول بعد الإضافة
            product_combo.set("اختر المنتج")
            price_entry.delete(0, tk.END)
            qty_entry.delete(0, tk.END)
            available_qty_label.config(text="")

            # تحديث فوري للواجهة
            force_ui_update()

            # تحديث قائمة المنتجات لإظهار الكميات الجديدة
            refresh_product_list()

            # تحديث إضافي للواجهة
            root.after(100, force_ui_update)

            # التركيز على اختيار المنتج التالي
            product_combo.focus()

        except Exception as e:
            messagebox.showerror("❌ خطأ", f"حدث خطأ: {str(e)}")

    def remove_from_cart():
        selected = cart_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عنصر من السلة لحذفه")
            return

        # حذف من القائمة والجدول
        index = cart_tree.index(selected[0])
        removed_item = cart.pop(index)
        cart_tree.delete(selected[0])
        update_total()

        # تحديث فوري للواجهة
        force_ui_update()

        # تحديث حالة النظام
        update_status(f"🗑️ تم حذف {removed_item[0]} من السلة", "#e74c3c")

    def update_total():
        total = sum(item[3] for item in cart)
        item_count = len(cart)
        if item_count > 0:
            total_var.set(f"Ghc {total:.2f} ({item_count} منتج)")
        else:
            total_var.set("0.00")

    def complete_sale():
        """إنهاء البيع السريع وطباعة الفاتورة"""
        if not cart:
            messagebox.showwarning("⚠️ تحذير", "السلة فارغة! يرجى إضافة منتجات أولاً")
            return

        # استخدام بيانات افتراضية إذا لم يتم إدخالها
        customer = customer_entry.get().strip()
        if not customer or customer == "عميل":
            customer = f"عميل-{datetime.now().strftime('%H%M%S')}"
            customer_entry.delete(0, tk.END)
            customer_entry.insert(0, customer)

        phone = phone_entry.get().strip()
        method = method_combo.get()
        clean_method = clean_payment_method(method)  # تنظيف طريقة الدفع

        # تأكيد البيع
        total_amount = sum(item[3] for item in cart)
        confirm_msg = f"🛒 تأكيد إنهاء البيع\n\n"
        confirm_msg += f"👤 العميل: {customer}\n"
        confirm_msg += f"📞 الهاتف: {phone if phone else 'غير محدد'}\n"
        confirm_msg += f"💳 طريقة الدفع: {method}\n"
        confirm_msg += f"🛍️ عدد المنتجات: {len(cart)}\n"
        confirm_msg += f"💰 الإجمالي: Ghc {total_amount:.2f}\n\n"
        confirm_msg += "هل تريد إنهاء البيع وطباعة الفاتورة؟"

        if messagebox.askyesno("🧾 تأكيد البيع", confirm_msg):
            create_invoice()

    def quick_complete_sale():
        """إنهاء البيع السريع بدون تأكيد"""
        if not cart:
            messagebox.showwarning("⚠️ تحذير", "السلة فارغة! يرجى إضافة منتجات أولاً")
            return

        # استخدام بيانات افتراضية
        if not customer_entry.get().strip() or customer_entry.get().strip() == "عميل":
            customer_entry.delete(0, tk.END)
            customer_entry.insert(0, f"عميل-{datetime.now().strftime('%H%M%S')}")

        create_invoice()

    def create_invoice():
        if not cart:
            messagebox.showwarning("⚠️ تحذير", "السلة فارغة! يرجى إضافة منتجات أولاً")
            return

        # التحقق الشامل من بيانات العميل
        valid, errors = validate_sales_data(
            "منتج مؤقت",  # لن يتم التحقق من المنتج هنا
            "1",  # سعر مؤقت
            "1",  # كمية مؤقتة
            customer_entry.get(),
            phone_entry.get()
        )

        # إزالة أخطاء المنتج والسعر والكمية لأنها تم التحقق منها مسبقاً
        customer_errors = [error for error in errors if any(word in error for word in ["العميل", "الهاتف"])]

        if customer_errors:
            error_message = "يرجى تصحيح الأخطاء التالية:\n\n" + "\n".join(f"• {error}" for error in customer_errors)
            messagebox.showerror("❌ خطأ في البيانات", error_message)
            return

        method = method_combo.get()
        clean_method = clean_payment_method(method)  # تنظيف طريقة الدفع
        phone = phone_entry.get().strip()
        customer = customer_entry.get().strip()
        now = datetime.now()

        try:
            db = connect_db()
            cursor = db.cursor()

            # إنشاء رقم فاتورة فريد
            invoice_id = f"INV-{now.strftime('%Y%m%d%H%M%S')}"
            total_amount = sum(item[3] for item in cart)

            # حفظ تفاصيل المخزون قبل التحديث لعرض التغييرات
            inventory_changes = []

            for item in cart:
                # جلب الكمية الحالية قبل التحديث
                cursor.execute("SELECT quantity FROM inventory WHERE product_name = %s", (item[0],))
                old_qty = cursor.fetchone()[0]
                new_qty = old_qty - item[2]
                inventory_changes.append((item[0], old_qty, new_qty, item[2]))

                # إدراج في جدول المبيعات
                cursor.execute("""INSERT INTO sales
                               (invoice_id, product_name, price, qty, total, time, payment_method, customer, phone, date)
                               VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                               (invoice_id, item[0], item[1], item[2], item[3],
                                now.strftime("%H:%M:%S"), clean_method, customer, phone, now.strftime("%Y-%m-%d")))

                # تحديث المخزون
                cursor.execute("UPDATE inventory SET quantity = quantity - %s WHERE product_name = %s",
                              (item[2], item[0]))

            db.commit()
            cursor.close()
            db.close()

            # طباعة الفاتورة
            print_invoice(invoice_id, cart, customer, phone, clean_method, now)

            # عرض تقرير التحديثات
            show_sale_summary(invoice_id, cart, customer, phone, method, total_amount)

            # مسح السلة
            cart_tree.delete(*cart_tree.get_children())
            cart.clear()
            update_total()

            # مسح بيانات العميل
            customer_entry.delete(0, tk.END)
            customer_entry.insert(0, "عميل")
            phone_entry.delete(0, tk.END)

            # تحديث فوري شامل للواجهة
            force_ui_update()

            # تحديث قائمة المنتجات لإظهار الكميات الجديدة
            refresh_product_list()

            # تحديث جميع الواجهات الأخرى باستخدام النظام الجديد
            trigger_global_update()

            # تحديث إضافي للوحة التحكم بعد ثانية واحدة
            root.after(1000, lambda: trigger_global_update())

            # تحديث إضافي للواجهة بعد التحديث
            root.after(200, force_ui_update)
            root.after(500, force_ui_update)

            # تحديث حالة النظام
            update_status(f"✅ تم إنشاء الفاتورة {invoice_id}", "#27ae60")

            messagebox.showinfo("✅ تم", f"تم إنشاء الفاتورة بنجاح!\nرقم الفاتورة: {invoice_id}\n\n📦 تم تحديث المخزون\n🧾 تمت إضافة الفاتورة للسجلات")

        except Exception as e:
            messagebox.showerror("❌ خطأ", f"حدث خطأ في إنشاء الفاتورة: {str(e)}")

    # إطار الأزرار مع تصميم محسن
    buttons_frame = tk.Frame(frame, bg="#f8f9fa")
    buttons_frame.pack(pady=20)

    # الصف الأول من الأزرار
    row1_frame = tk.Frame(buttons_frame, bg="#f8f9fa")
    row1_frame.pack(pady=5)

    tk.Button(row1_frame, text="➕ إضافة للسلة (F1)", command=add_to_cart,
             bg="#3498db", fg="white", font=("Arial", 11, "bold"),
             width=18, height=2, relief="flat", cursor="hand2").pack(side="left", padx=5)

    tk.Button(row1_frame, text="🗑️ حذف من السلة (F2)", command=remove_from_cart,
             bg="#e74c3c", fg="white", font=("Arial", 11, "bold"),
             width=18, height=2, relief="flat", cursor="hand2").pack(side="left", padx=5)

    tk.Button(row1_frame, text="🧾 إنشاء فاتورة (F3)", command=create_invoice,
             bg="#27ae60", fg="white", font=("Arial", 11, "bold"),
             width=18, height=2, relief="flat", cursor="hand2").pack(side="left", padx=5)

    # الصف الثاني من الأزرار
    row2_frame = tk.Frame(buttons_frame, bg="#f8f9fa")
    row2_frame.pack(pady=5)

    tk.Button(row2_frame, text="⚡ بيع سريع (F4)", command=quick_complete_sale,
             bg="#9b59b6", fg="white", font=("Arial", 12, "bold"),
             width=20, height=2, relief="flat", cursor="hand2").pack(side="left", padx=5)

    tk.Button(row2_frame, text="🔄 مسح السلة (F5)", command=lambda: clear_cart(),
             bg="#95a5a6", fg="white", font=("Arial", 12, "bold"),
             width=20, height=2, relief="flat", cursor="hand2").pack(side="left", padx=5)

    # ربط اختصارات لوحة المفاتيح
    def bind_shortcuts():
        root.bind("<F1>", lambda e: add_to_cart())
        root.bind("<F2>", lambda e: remove_from_cart())
        root.bind("<F3>", lambda e: create_invoice())
        root.bind("<F4>", lambda e: quick_complete_sale())
        root.bind("<F5>", lambda e: clear_cart())
        root.bind("<Control-Return>", lambda e: complete_sale())

    bind_shortcuts()

    def clear_cart():
        """مسح جميع محتويات السلة"""
        if cart:
            if messagebox.askyesno("🗑️ مسح السلة", "هل تريد مسح جميع المنتجات من السلة؟"):
                cart.clear()
                cart_tree.delete(*cart_tree.get_children())
                update_total()
                messagebox.showinfo("✅ تم", "تم مسح السلة بنجاح")
        else:
            messagebox.showinfo("ℹ️ معلومات", "السلة فارغة بالفعل")

    def refresh_product_list():
        """تحديث قائمة المنتجات لإظهار الكميات الجديدة"""
        try:
            # تحديث قائمة المنتجات في الـ combobox
            product_names = load_product_names()
            product_combo['values'] = product_names

            # فرض تحديث الواجهة
            product_combo.update()
            root.update_idletasks()

            # إذا كان هناك منتج محدد، تحديث الكمية المتاحة
            if product_combo.get() and product_combo.get() != "اختر المنتج":
                selected_product = product_combo.get()
                db = connect_db()
                cursor = db.cursor()
                cursor.execute("SELECT quantity FROM inventory WHERE product_name = %s", (selected_product,))
                result = cursor.fetchone()
                if result:
                    available_qty_label.config(text=f"متاح: {result[0]}")
                    available_qty_label.update()
                cursor.close()
                db.close()

            # تحديث شريط الحالة
            update_status("🔄 تم تحديث قائمة المنتجات", "#3498db")

        except Exception as e:
            print(f"خطأ في تحديث قائمة المنتجات: {e}")
            update_status("❌ خطأ في تحديث المنتجات", "#e74c3c")

    def show_sale_summary(invoice_id, cart_items, customer, phone, method, total_amount):
        """عرض ملخص البيع والتحديثات"""
        # إنشاء نافذة ملخص البيع
        summary_window = tk.Toplevel(root)
        summary_window.title("📊 ملخص البيع")
        summary_window.geometry("600x500")
        summary_window.configure(bg="#f8f9fa")
        summary_window.resizable(False, False)

        # جعل النافذة في المقدمة
        summary_window.transient(root)
        summary_window.grab_set()

        # إطار العنوان
        header_frame = tk.Frame(summary_window, bg="#27ae60", height=60)
        header_frame.pack(fill="x")
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="✅ تمت عملية البيع بنجاح!",
                font=("Arial", 16, "bold"), bg="#27ae60", fg="white").pack(expand=True)

        # إطار المعلومات
        info_frame = tk.Frame(summary_window, bg="#ffffff", relief="raised", bd=2)
        info_frame.pack(pady=20, padx=20, fill="both", expand=True)

        # معلومات الفاتورة
        tk.Label(info_frame, text="🧾 معلومات الفاتورة",
                font=("Arial", 14, "bold"), bg="#ffffff", fg="#2c3e50").pack(pady=10)

        info_text = f"""
رقم الفاتورة: {invoice_id}
العميل: {customer}
الهاتف: {phone if phone else 'غير محدد'}
طريقة الدفع: {method}
عدد المنتجات: {len(cart_items)}
الإجمالي: Ghc {total_amount:.2f}
        """

        tk.Label(info_frame, text=info_text, font=("Arial", 11),
                bg="#ffffff", fg="#34495e", justify="right").pack(pady=10)

        # جدول المنتجات المباعة
        tk.Label(info_frame, text="📦 المنتجات المباعة وتحديث المخزون",
                font=("Arial", 12, "bold"), bg="#ffffff", fg="#2c3e50").pack(pady=(20, 10))

        # إنشاء جدول للمنتجات
        columns = ("المنتج", "الكمية المباعة", "السعر", "الإجمالي")
        products_tree = ttk.Treeview(info_frame, columns=columns, show="headings", height=8)

        for col in columns:
            products_tree.heading(col, text=col)
            products_tree.column(col, width=120, anchor="center")

        # إضافة المنتجات للجدول
        for item in cart_items:
            products_tree.insert("", "end", values=(
                item[0],  # اسم المنتج
                item[2],  # الكمية
                f"Ghc {item[1]:.2f}",  # السعر
                f"Ghc {item[3]:.2f}"   # الإجمالي
            ))

        products_tree.pack(pady=10, padx=10, fill="both", expand=True)

        # أزرار الإجراءات
        buttons_frame = tk.Frame(summary_window, bg="#f8f9fa")
        buttons_frame.pack(pady=20)

        tk.Button(buttons_frame, text="🖨️ طباعة الفاتورة مرة أخرى",
                 command=lambda: reprint_invoice(invoice_id),
                 bg="#3498db", fg="white", font=("Arial", 11, "bold"),
                 width=20, relief="flat", cursor="hand2").pack(side="left", padx=10)

        tk.Button(buttons_frame, text="📊 عرض الفواتير",
                 command=lambda: [summary_window.destroy(), show_invoices_page()],
                 bg="#9b59b6", fg="white", font=("Arial", 11, "bold"),
                 width=15, relief="flat", cursor="hand2").pack(side="left", padx=10)

        tk.Button(buttons_frame, text="✅ إغلاق",
                 command=summary_window.destroy,
                 bg="#95a5a6", fg="white", font=("Arial", 11, "bold"),
                 width=10, relief="flat", cursor="hand2").pack(side="left", padx=10)

        # إغلاق النافذة تلقائياً بعد 10 ثوانٍ
        summary_window.after(10000, summary_window.destroy)

    def reprint_invoice(invoice_id):
        """إعادة طباعة الفاتورة"""
        try:
            db = connect_db()
            cursor = db.cursor()
            cursor.execute("""SELECT product_name, price, qty, total, customer, phone, payment_method, date, time
                             FROM sales WHERE invoice_id = %s""", (invoice_id,))
            invoice_data = cursor.fetchall()
            cursor.close()
            db.close()

            if invoice_data:
                customer_name = invoice_data[0][4]
                phone = invoice_data[0][5]
                payment_method = invoice_data[0][6]
                invoice_date = datetime.strptime(f"{invoice_data[0][7]} {invoice_data[0][8]}", "%Y-%m-%d %H:%M:%S")

                cart_items = [(row[0], row[1], row[2], row[3]) for row in invoice_data]

                # طباعة الفاتورة
                print_invoice(invoice_id, cart_items, customer_name, phone, payment_method, invoice_date)
                messagebox.showinfo("✅ تم", "تم إعادة طباعة الفاتورة")
            else:
                messagebox.showerror("❌ خطأ", "لم يتم العثور على الفاتورة")

        except Exception as e:
            messagebox.showerror("❌ خطأ", f"حدث خطأ أثناء إعادة الطباعة: {str(e)}")

    def show_invoices_page():
        """عرض صفحة الفواتير"""
        # هذه الدالة ستقوم بالتبديل إلى صفحة الفواتير
        # يمكن تنفيذها عبر استدعاء دالة التنقل في main.py
        messagebox.showinfo("ℹ️ معلومات", "انتقل إلى قسم 'الفواتير' من القائمة الجانبية لعرض جميع الفواتير")

    return frame
