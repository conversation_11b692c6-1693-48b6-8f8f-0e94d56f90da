import re
from datetime import datetime

def validate_product_name(name):
    """التحقق من صحة اسم المنتج"""
    if not name or not name.strip():
        return False, "اسم المنتج مطلوب"
    
    if len(name.strip()) < 2:
        return False, "اسم المنتج يجب أن يكون أكثر من حرفين"
    
    if len(name.strip()) > 100:
        return False, "اسم المنتج طويل جداً (الحد الأقصى 100 حرف)"
    
    return True, ""

def validate_category(category):
    """التحقق من صحة فئة المنتج"""
    if not category or not category.strip():
        return False, "فئة المنتج مطلوبة"
    
    if len(category.strip()) < 2:
        return False, "فئة المنتج يجب أن تكون أكثر من حرفين"
    
    if len(category.strip()) > 50:
        return False, "فئة المنتج طويلة جداً (الحد الأقصى 50 حرف)"
    
    return True, ""

def validate_price(price_str, field_name="السعر"):
    """التحقق من صحة السعر"""
    if not price_str or not price_str.strip():
        return False, f"{field_name} مطلوب"
    
    try:
        price = float(price_str.strip())
        if price < 0:
            return False, f"{field_name} يجب أن يكون أكبر من أو يساوي صفر"
        
        if price > 999999.99:
            return False, f"{field_name} كبير جداً"
        
        # التحقق من عدد الأرقام العشرية
        if '.' in price_str and len(price_str.split('.')[1]) > 2:
            return False, f"{field_name} يجب أن يحتوي على رقمين عشريين كحد أقصى"
        
        return True, ""
    
    except ValueError:
        return False, f"{field_name} يجب أن يكون رقماً صحيحاً"

def validate_quantity(qty_str):
    """التحقق من صحة الكمية"""
    if not qty_str or not qty_str.strip():
        return False, "الكمية مطلوبة"
    
    try:
        qty = int(qty_str.strip())
        if qty < 0:
            return False, "الكمية يجب أن تكون أكبر من أو تساوي صفر"
        
        if qty > 999999:
            return False, "الكمية كبيرة جداً"
        
        return True, ""
    
    except ValueError:
        return False, "الكمية يجب أن تكون رقماً صحيحاً"

def validate_expiry_date(date_str):
    """التحقق من صحة تاريخ الانتهاء"""
    if not date_str or not date_str.strip():
        return False, "تاريخ الانتهاء مطلوب"
    
    # التحقق من تنسيق التاريخ MM/YYYY
    pattern = r'^(0[1-9]|1[0-2])\/\d{4}$'
    if not re.match(pattern, date_str.strip()):
        return False, "تنسيق التاريخ يجب أن يكون MM/YYYY (مثال: 12/2024)"
    
    try:
        # التحقق من صحة التاريخ
        month, year = date_str.strip().split('/')
        month = int(month)
        year = int(year)
        
        # التحقق من السنة
        current_year = datetime.now().year
        if year < current_year:
            return False, "تاريخ الانتهاء لا يمكن أن يكون في الماضي"
        
        if year > current_year + 20:
            return False, "تاريخ الانتهاء بعيد جداً في المستقبل"
        
        # التحقق من الشهر
        if month < 1 or month > 12:
            return False, "الشهر يجب أن يكون بين 1 و 12"
        
        return True, ""
    
    except ValueError:
        return False, "تاريخ غير صحيح"

def validate_customer_name(name):
    """التحقق من صحة اسم العميل"""
    if not name or not name.strip():
        return False, "اسم العميل مطلوب"
    
    if len(name.strip()) < 2:
        return False, "اسم العميل يجب أن يكون أكثر من حرفين"
    
    if len(name.strip()) > 100:
        return False, "اسم العميل طويل جداً (الحد الأقصى 100 حرف)"
    
    # التحقق من وجود أحرف صحيحة فقط
    if not re.match(r'^[a-zA-Zأ-ي\s]+$', name.strip()):
        return False, "اسم العميل يجب أن يحتوي على أحرف فقط"
    
    return True, ""

def validate_phone_number(phone):
    """التحقق من صحة رقم الهاتف"""
    if not phone or not phone.strip():
        return True, ""  # رقم الهاتف اختياري
    
    # إزالة المسافات والرموز
    phone_clean = re.sub(r'[\s\-\(\)]', '', phone.strip())
    
    # التحقق من أن الرقم يحتوي على أرقام فقط
    if not phone_clean.isdigit():
        return False, "رقم الهاتف يجب أن يحتوي على أرقام فقط"
    
    # التحقق من طول الرقم
    if len(phone_clean) < 8:
        return False, "رقم الهاتف قصير جداً"
    
    if len(phone_clean) > 15:
        return False, "رقم الهاتف طويل جداً"
    
    return True, ""

def validate_inventory_data(name, category, wholesale_price, selling_price, quantity, expiry_date):
    """التحقق الشامل من بيانات المخزون"""
    errors = []
    
    # التحقق من اسم المنتج
    valid, error = validate_product_name(name)
    if not valid:
        errors.append(error)
    
    # التحقق من الفئة
    valid, error = validate_category(category)
    if not valid:
        errors.append(error)
    
    # التحقق من سعر الجملة
    valid, error = validate_price(wholesale_price, "سعر الجملة")
    if not valid:
        errors.append(error)
    
    # التحقق من سعر البيع
    valid, error = validate_price(selling_price, "سعر البيع")
    if not valid:
        errors.append(error)
    else:
        # التحقق من أن سعر البيع أكبر من سعر الجملة
        try:
            wp = float(wholesale_price)
            sp = float(selling_price)
            if sp < wp:
                errors.append("سعر البيع يجب أن يكون أكبر من أو يساوي سعر الجملة")
        except ValueError:
            pass  # سيتم التعامل مع هذا الخطأ في التحقق من الأسعار
    
    # التحقق من الكمية
    valid, error = validate_quantity(quantity)
    if not valid:
        errors.append(error)
    
    # التحقق من تاريخ الانتهاء
    valid, error = validate_expiry_date(expiry_date)
    if not valid:
        errors.append(error)
    
    return len(errors) == 0, errors

def validate_sales_data(product_name, price, quantity, customer_name, phone):
    """التحقق الشامل من بيانات المبيعات"""
    errors = []
    
    # التحقق من اختيار المنتج
    if not product_name or product_name == "اختر المنتج":
        errors.append("يرجى اختيار منتج")
    
    # التحقق من السعر
    valid, error = validate_price(price, "السعر")
    if not valid:
        errors.append(error)
    
    # التحقق من الكمية
    valid, error = validate_quantity(quantity)
    if not valid:
        errors.append(error)
    
    # التحقق من اسم العميل
    valid, error = validate_customer_name(customer_name)
    if not valid:
        errors.append(error)
    
    # التحقق من رقم الهاتف
    valid, error = validate_phone_number(phone)
    if not valid:
        errors.append(error)
    
    return len(errors) == 0, errors
