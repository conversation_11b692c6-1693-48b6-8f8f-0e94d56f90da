# 🏥 نظام إدارة الصيدلية - Pharmacy Management System

نظام شامل لإدارة الصيدليات مع واجهة مستخدم حديثة ونظام طباعة فواتير متقدم.

## ✨ الميزات الرئيسية

### 📦 إدارة المخزون
- إضافة وتحديث وحذف المنتجات
- تتبع الكميات وتواريخ الانتهاء
- إحصائيات شاملة للمخزون
- التحقق من صحة البيانات

### 🛒 نظام المبيعات
- واجهة بيع سهلة الاستخدام
- سلة تسوق تفاعلية
- التحقق من توفر المخزون
- دعم طرق دفع متعددة

### 🧾 إدارة الفواتير
- إنشاء فواتير مفصلة
- طباعة تلقائية للفواتير
- البحث والتصفية في الفواتير
- إعادة طباعة الفواتير السابقة

### 📊 لوحة التحكم
- إحصائيات شاملة ومرئية
- تتبع المبيعات اليومية
- مراقبة قيمة المخزون
- تقارير سريعة

### 👥 إدارة المستخدمين
- نظام تسجيل دخول آمن
- إدارة صلاحيات المستخدمين
- تتبع أنشطة المستخدمين

## 🚀 التثبيت والإعداد

### المتطلبات الأساسية
- Python 3.6 أو أحدث
- MySQL Server 5.7 أو أحدث
- نظام تشغيل Windows/Linux/macOS

### خطوات التثبيت

1. **تحميل المشروع**
   ```bash
   git clone [repository-url]
   cd pharmacy-management-system
   ```

2. **تثبيت المتطلبات**
   ```bash
   pip install -r requirements.txt
   ```

3. **إعداد قاعدة البيانات**
   ```bash
   python setup.py
   ```

4. **تشغيل النظام**
   ```bash
   python main.py
   ```

### بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## 📱 لقطات الشاشة

### لوحة التحكم
- إحصائيات شاملة مع بطاقات ملونة
- عرض المبيعات اليومية والإجمالية
- مراقبة حالة المخزون

### واجهة المخزون
- نموذج إدخال محسن مع التحقق من البيانات
- جدول تفاعلي مع إمكانية البحث
- أزرار واضحة مع أيقونات

### نظام المبيعات
- واجهة بيع سهلة ومرتبة
- سلة تسوق تفاعلية
- حساب تلقائي للإجماليات

### إدارة الفواتير
- عرض جميع الفواتير مع إمكانية البحث
- طباعة فورية للفواتير
- تصفية حسب التاريخ والعميل

## 🎨 التصميم والواجهة

### نظام الألوان
- **الأساسي:** #2c3e50 (أزرق داكن)
- **النجاح:** #27ae60 (أخضر)
- **الخطأ:** #e74c3c (أحمر)
- **التحذير:** #f39c12 (برتقالي)
- **المعلومات:** #3498db (أزرق فاتح)

### الأيقونات والرموز
- استخدام أيقونات نصية واضحة
- رموز مفهومة لجميع العمليات
- تصميم متناسق عبر النظام

## 🔧 الإعدادات المتقدمة

### إعدادات قاعدة البيانات
يمكن تعديل إعدادات الاتصال في ملف `config.py`:
```python
DB_HOST = "localhost"
DB_USER = "root"
DB_PASSWORD = ""
DB_NAME = "pharmacy_db"
```

### إعدادات الطباعة
- تخصيص قالب الفاتورة
- إعدادات الطباعة التلقائية
- تحديد مجلد حفظ الفواتير

## 📚 الوثائق

- **[دليل المستخدم](USER_GUIDE.md)** - شرح مفصل لاستخدام النظام
- **[تقرير التحسينات](README_IMPROVEMENTS.md)** - تفاصيل التحسينات المنجزة
- **[ملف المتطلبات](requirements.txt)** - قائمة المكتبات المطلوبة

## 🛠️ الملفات الرئيسية

```
pharmacy-management-system/
├── main.py                 # الملف الرئيسي
├── inventory.py           # إدارة المخزون
├── sales.py              # نظام المبيعات
├── invoice.py            # إدارة الفواتير
├── dashboard.py          # لوحة التحكم
├── users.py              # إدارة المستخدمين
├── validation.py         # التحقق من البيانات
├── theme.py              # نظام الألوان والتصميم
├── setup.py              # إعداد النظام
├── update_database.py    # تحديث قاعدة البيانات
├── config.py             # ملف الإعدادات
└── requirements.txt      # المتطلبات
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**خطأ في الاتصال بقاعدة البيانات:**
- تأكد من تشغيل MySQL Server
- تحقق من بيانات الاتصال
- تأكد من وجود قاعدة البيانات

**مشاكل في الطباعة:**
- تأكد من وجود برنامج لفتح الملفات النصية
- تحقق من إعدادات الطابعة
- تأكد من صلاحيات الكتابة في المجلد

**أخطاء في واجهة المستخدم:**
- تأكد من تثبيت tkinter
- تحقق من دقة الشاشة
- أعد تشغيل النظام

## 🤝 المساهمة

نرحب بالمساهمات لتحسين النظام:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push للفرع
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 الدعم والتواصل

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- مراجعة الوثائق المرفقة
- التحقق من الأسئلة الشائعة

## 🎯 الإصدارات المستقبلية

### الميزات المخططة:
- [ ] تقارير مفصلة وإحصائيات متقدمة
- [ ] نظام تنبيهات للمنتجات منتهية الصلاحية
- [ ] دعم الباركود
- [ ] واجهة ويب
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة المحاسبة

---

**تم تطوير هذا النظام بعناية لتلبية احتياجات الصيدليات الحديثة** 💊

**النسخة:** 2.0  
**آخر تحديث:** 2024  
**المطور:** فريق تطوير نظم إدارة الصيدليات
