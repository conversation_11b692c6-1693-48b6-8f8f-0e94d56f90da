# -*- coding: utf-8 -*-
import tkinter as tk
from tkinter import ttk, messagebox
import mysql.connector
from user_management import create_user_management_page

def connect_db():
    return mysql.connector.connect(
        host="localhost",
        user="root",
        password="",
        database="pharmacy_db"
    )

def users_page(root):
    """صفحة إدارة المستخدمين المحسنة"""
    return create_user_management_page(root)
