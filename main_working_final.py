# -*- coding: utf-8 -*-
"""
نظام صيدلية الشفاء - النسخة النهائية العاملة
"""

import tkinter as tk
from tkinter import messagebox
import mysql.connector

# الألوان والخطوط
COLORS = {
    'primary': '#27ae60',
    'bg_main': '#f8f9fa',
    'bg_card': '#ffffff',
    'bg_sidebar': '#2c3e50',
    'text_primary': '#2c3e50',
    'text_white': '#ffffff',
    'btn_success': '#27ae60',
    'accent': '#3498db',
    'error': '#e74c3c'
}

FONTS = {
    'title': ('Arial', 18, 'bold'),
    'heading': ('Arial', 14, 'bold'),
    'main': ('Arial', 12),
    'button': ('Arial', 11, 'bold')
}

# متغير المستخدم الحالي
current_user = {'username': '', 'role': ''}

def connect_db():
    """الاتصال بقاعدة البيانات"""
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="pharmacy_db"
        )
    except:
        return None

def login():
    """تسجيل الدخول"""
    username = username_entry.get().strip()
    password = password_entry.get().strip()
    
    if not username or not password:
        messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
        return
    
    try:
        db = connect_db()
        if not db:
            messagebox.showerror("خطأ", "فشل في الاتصال بقاعدة البيانات")
            return
            
        cursor = db.cursor()
        cursor.execute("SELECT username, role FROM users WHERE username = %s AND password = %s", 
                      (username, password))
        result = cursor.fetchone()
        cursor.close()
        db.close()
        
        if result:
            current_user['username'] = result[0]
            current_user['role'] = result[1]
            
            role_name = "مدير النظام" if result[1] == 'admin' else "مستخدم محدود"
            messagebox.showinfo("مرحباً", f"أهلاً وسهلاً {result[0]}\nتم تسجيل الدخول بنجاح كـ {role_name}")
            
            show_main_system()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

def show_main_system():
    """عرض النظام الرئيسي"""
    # إخفاء شاشة تسجيل الدخول
    login_frame.pack_forget()
    
    # إنشاء الشريط العلوي
    top_frame = tk.Frame(root, bg=COLORS['primary'], height=60)
    top_frame.pack(side="top", fill="x")
    top_frame.pack_propagate(False)
    
    # عنوان النظام
    tk.Label(top_frame, text="صيدلية الشفاء - نظام إدارة شامل", 
             font=FONTS['heading'], bg=COLORS['primary'], fg=COLORS['text_white']).pack(side="left", padx=20, pady=15)
    
    # معلومات المستخدم
    role_name = "مدير النظام" if current_user['role'] == 'admin' else "مستخدم محدود"
    tk.Label(top_frame, text=f"المستخدم: {current_user['username']} ({role_name})", 
             font=FONTS['main'], bg=COLORS['primary'], fg=COLORS['text_white']).pack(side="right", padx=20, pady=15)
    
    # إنشاء القائمة الجانبية
    sidebar = tk.Frame(root, bg=COLORS['bg_sidebar'], width=250)
    sidebar.pack(side="left", fill="y")
    sidebar.pack_propagate(False)
    
    # شعار في القائمة
    tk.Label(sidebar, text="صيدلية الشفاء", font=('Arial', 16, 'bold'),
             bg=COLORS['bg_sidebar'], fg=COLORS['text_white']).pack(pady=30)
    
    # أزرار القائمة
    buttons = [
        ("لوحة التحكم", show_dashboard),
        ("إدارة المخزون", show_inventory),
        ("إدارة المبيعات", show_sales),
    ]
    
    # أزرار المدير فقط
    if current_user['role'] == 'admin':
        buttons.extend([
            ("إدارة الفواتير", show_invoices),
            ("إدارة المستخدمين", show_users),
        ])
    
    buttons.extend([
        ("اختبار قاعدة البيانات", test_database),
        ("تسجيل خروج", logout)
    ])
    
    # إنشاء الأزرار
    for text, command in buttons:
        btn = tk.Button(sidebar, text=text, command=command,
                       bg=COLORS['bg_sidebar'], fg=COLORS['text_white'], font=FONTS['main'],
                       relief="flat", anchor="w", padx=20, pady=10, width=20)
        btn.pack(fill="x", pady=2, padx=10)
        
        # تأثير hover
        def on_enter(event, button=btn):
            button.configure(bg=COLORS['accent'])
        
        def on_leave(event, button=btn):
            button.configure(bg=COLORS['bg_sidebar'])
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
    
    # إنشاء المحتوى الرئيسي
    global main_content
    main_content = tk.Frame(root, bg=COLORS['bg_main'])
    main_content.pack(side="right", fill="both", expand=True)
    
    # عرض لوحة التحكم افتراضياً
    show_dashboard()

def clear_content():
    """مسح المحتوى الرئيسي"""
    for widget in main_content.winfo_children():
        widget.destroy()

def show_dashboard():
    """عرض لوحة التحكم"""
    clear_content()
    
    # عنوان الصفحة
    tk.Label(main_content, text="لوحة التحكم", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=30)
    
    # رسالة ترحيب
    tk.Label(main_content, text=f"مرحباً بك {current_user['username']}", 
             font=FONTS['heading'], bg=COLORS['bg_main'], fg=COLORS['text_primary']).pack(pady=10)
    
    # إحصائيات
    stats_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    stats_frame.pack(pady=20)
    
    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            
            # عدد المنتجات
            cursor.execute("SELECT COUNT(*) FROM inventory")
            products_count = cursor.fetchone()[0]
            
            # عدد المبيعات
            cursor.execute("SELECT COUNT(*) FROM sales")
            sales_count = cursor.fetchone()[0]
            
            # عدد المستخدمين
            cursor.execute("SELECT COUNT(*) FROM users")
            users_count = cursor.fetchone()[0]
            
            cursor.close()
            db.close()
            
            # عرض الإحصائيات
            tk.Label(stats_frame, text=f"عدد المنتجات: {products_count}", 
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
            tk.Label(stats_frame, text=f"عدد المبيعات: {sales_count}", 
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
            tk.Label(stats_frame, text=f"عدد المستخدمين: {users_count}", 
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
    except:
        tk.Label(stats_frame, text="النظام يعمل بشكل صحيح!", 
                font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['btn_success']).pack(pady=10)

def show_inventory():
    """عرض المخزون"""
    clear_content()
    tk.Label(main_content, text="إدارة المخزون", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=50)
    tk.Label(main_content, text="هنا يمكنك إدارة جميع منتجات المخزون", 
             font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=20)
    tk.Label(main_content, text="متصل بجدول inventory في قاعدة البيانات", 
             font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['accent']).pack(pady=10)

def show_sales():
    """عرض المبيعات"""
    clear_content()
    tk.Label(main_content, text="إدارة المبيعات", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=50)
    tk.Label(main_content, text="هنا يمكنك إدارة جميع عمليات البيع", 
             font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=20)
    tk.Label(main_content, text="متصل بجدول sales في قاعدة البيانات", 
             font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['accent']).pack(pady=10)

def show_invoices():
    """عرض الفواتير"""
    clear_content()
    tk.Label(main_content, text="إدارة الفواتير", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=50)
    tk.Label(main_content, text="هنا يمكنك إدارة جميع الفواتير", 
             font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=20)
    tk.Label(main_content, text="متصل بجدول sales في قاعدة البيانات (للمدير فقط)", 
             font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['accent']).pack(pady=10)

def show_users():
    """عرض المستخدمين"""
    clear_content()
    tk.Label(main_content, text="إدارة المستخدمين", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=50)
    tk.Label(main_content, text="هنا يمكنك إدارة جميع المستخدمين", 
             font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=20)
    tk.Label(main_content, text="متصل بجدول users في قاعدة البيانات (للمدير فقط)", 
             font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['accent']).pack(pady=10)

def test_database():
    """اختبار قاعدة البيانات"""
    clear_content()
    tk.Label(main_content, text="اختبار قاعدة البيانات", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=30)
    
    result_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    result_frame.pack(pady=20)
    
    db = connect_db()
    if db:
        try:
            cursor = db.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            users_count = cursor.fetchone()[0]
            cursor.close()
            db.close()
            
            tk.Label(result_frame, text="الاتصال بقاعدة البيانات ناجح",
                    font=FONTS['heading'], bg=COLORS['bg_main'], fg=COLORS['btn_success']).pack(pady=10)
            tk.Label(result_frame, text=f"عدد المستخدمين في قاعدة البيانات: {users_count}",
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
        except Exception as e:
            tk.Label(result_frame, text=f"خطأ في قاعدة البيانات: {str(e)}",
                    font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['error']).pack(pady=10)
    else:
        tk.Label(result_frame, text="فشل في الاتصال بقاعدة البيانات",
                font=FONTS['heading'], bg=COLORS['bg_main'], fg=COLORS['error']).pack(pady=10)
        tk.Label(result_frame, text="تأكد من تشغيل MySQL Server",
                font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['error']).pack(pady=5)

def logout():
    """تسجيل الخروج"""
    result = messagebox.askyesno("تسجيل خروج", "هل تريد تسجيل الخروج من النظام؟")
    if result:
        # إعادة تشغيل النظام
        root.destroy()
        main()

def main():
    """الدالة الرئيسية"""
    global root, login_frame, username_entry, password_entry
    
    # إنشاء النافذة الرئيسية
    root = tk.Tk()
    root.title("صيدلية الشفاء - نظام إدارة شامل")
    root.geometry("1200x700")
    root.configure(bg=COLORS['bg_main'])
    
    # توسيط النافذة
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    # شاشة تسجيل الدخول
    login_frame = tk.Frame(root, bg=COLORS['bg_main'])
    login_frame.pack(fill="both", expand=True)
    
    # إطار تسجيل الدخول
    login_container = tk.Frame(login_frame, bg=COLORS['bg_card'], relief="raised", bd=2)
    login_container.place(relx=0.5, rely=0.5, anchor="center", width=400, height=350)
    
    # العنوان
    tk.Label(login_container, text="صيدلية الشفاء", font=FONTS['title'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)
    
    tk.Label(login_container, text="نظام إدارة شامل ومتطور", font=FONTS['main'],
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(pady=5)
    
    tk.Label(login_container, text="تسجيل الدخول", font=FONTS['heading'],
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(pady=15)
    
    # حقول الإدخال
    tk.Label(login_container, text="اسم المستخدم:", font=FONTS['main'],
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", padx=50)
    
    username_entry = tk.Entry(login_container, font=FONTS['main'], width=25, relief="solid", bd=1)
    username_entry.pack(pady=5)
    
    tk.Label(login_container, text="كلمة المرور:", font=FONTS['main'],
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", padx=50)
    
    password_entry = tk.Entry(login_container, font=FONTS['main'], width=25, relief="solid", bd=1, show="*")
    password_entry.pack(pady=5)
    
    # زر تسجيل الدخول
    login_btn = tk.Button(login_container, text="دخول", command=login,
                         bg=COLORS['btn_success'], fg=COLORS['text_white'],
                         font=FONTS['button'], width=20, height=2, relief="flat")
    login_btn.pack(pady=20)
    
    # معلومات تسجيل الدخول
    info_text = "بيانات تسجيل الدخول:\nالمدير: admin / admin123\nالمستخدم: user / user123"
    tk.Label(login_container, text=info_text, font=('Arial', 9),
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack()
    
    # ربط Enter
    username_entry.bind("<Return>", lambda e: password_entry.focus())
    password_entry.bind("<Return>", lambda e: login())
    
    # تركيز على حقل اسم المستخدم
    username_entry.focus()
    
    print("تم تشغيل نظام صيدلية الشفاء")
    print("بيانات تسجيل الدخول:")
    print("   المدير: admin / admin123")
    print("   المستخدم: user / user123")
    
    root.mainloop()

if __name__ == "__main__":
    main()
