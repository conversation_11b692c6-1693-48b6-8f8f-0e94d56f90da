# -*- coding: utf-8 -*-
"""
نظام صيدلية الشفاء - الملف الرئيسي العامل
"""

import tkinter as tk
from tkinter import messagebox
import mysql.connector
from datetime import datetime

# الألوان والخطوط
COLORS = {
    'primary': '#27ae60',
    'bg_main': '#f8f9fa',
    'bg_card': '#ffffff',
    'bg_sidebar': '#2c3e50',
    'text_primary': '#2c3e50',
    'text_white': '#ffffff',
    'btn_success': '#27ae60',
    'accent': '#3498db',
    'error': '#e74c3c'
}

FONTS = {
    'title': ('Arial', 18, 'bold'),
    'heading': ('Arial', 14, 'bold'),
    'main': ('Arial', 12),
    'button': ('Arial', 11, 'bold')
}

# متغير المستخدم الحالي
current_user = {'username': '', 'role': ''}

def connect_db():
    """الاتصال بقاعدة البيانات"""
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="pharmacy_db"
        )
    except:
        return None

def login():
    """تسجيل الدخول"""
    username = username_entry.get().strip()
    password = password_entry.get().strip()
    
    if not username or not password:
        messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
        return
    
    try:
        db = connect_db()
        if not db:
            messagebox.showerror("خطأ", "فشل في الاتصال بقاعدة البيانات")
            return
            
        cursor = db.cursor()
        cursor.execute("SELECT username, role FROM users WHERE username = %s AND password = %s", 
                      (username, password))
        result = cursor.fetchone()
        cursor.close()
        db.close()
        
        if result:
            current_user['username'] = result[0]
            current_user['role'] = result[1]
            
            role_name = "مدير النظام" if result[1] == 'admin' else "مستخدم محدود"
            messagebox.showinfo("مرحباً", f"أهلاً وسهلاً {result[0]}\nتم تسجيل الدخول بنجاح كـ {role_name}")
            
            show_main_system()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

def show_main_system():
    """عرض النظام الرئيسي"""
    # إخفاء شاشة تسجيل الدخول
    login_frame.pack_forget()
    
    # إنشاء الشريط العلوي
    top_frame = tk.Frame(root, bg=COLORS['primary'], height=60)
    top_frame.pack(side="top", fill="x")
    top_frame.pack_propagate(False)
    
    # عنوان النظام
    tk.Label(top_frame, text="صيدلية الشفاء - نظام إدارة شامل", 
             font=FONTS['heading'], bg=COLORS['primary'], fg=COLORS['text_white']).pack(side="left", padx=20, pady=15)
    
    # معلومات المستخدم
    role_name = "مدير النظام" if current_user['role'] == 'admin' else "مستخدم محدود"
    tk.Label(top_frame, text=f"المستخدم: {current_user['username']} ({role_name})", 
             font=FONTS['main'], bg=COLORS['primary'], fg=COLORS['text_white']).pack(side="right", padx=20, pady=15)
    
    # إنشاء القائمة الجانبية
    sidebar = tk.Frame(root, bg=COLORS['bg_sidebar'], width=250)
    sidebar.pack(side="left", fill="y")
    sidebar.pack_propagate(False)
    
    # شعار في القائمة
    tk.Label(sidebar, text="صيدلية الشفاء", font=('Arial', 16, 'bold'),
             bg=COLORS['bg_sidebar'], fg=COLORS['text_white']).pack(pady=30)
    
    # أزرار القائمة
    buttons = [
        ("لوحة التحكم", show_dashboard),
        ("إدارة المخزون", show_inventory),
        ("إدارة المبيعات", show_sales),
    ]
    
    # أزرار المدير فقط
    if current_user['role'] == 'admin':
        buttons.extend([
            ("إدارة الفواتير", show_invoices),
            ("إدارة المستخدمين", show_users),
        ])
    
    buttons.extend([
        ("اختبار قاعدة البيانات", test_database),
        ("تسجيل خروج", logout)
    ])
    
    # إنشاء الأزرار
    for text, command in buttons:
        btn = tk.Button(sidebar, text=text, command=command,
                       bg=COLORS['bg_sidebar'], fg=COLORS['text_white'], font=FONTS['main'],
                       relief="flat", anchor="w", padx=20, pady=10, width=20)
        btn.pack(fill="x", pady=2, padx=10)
        
        # تأثير hover
        def on_enter(event, button=btn):
            button.configure(bg=COLORS['accent'])
        
        def on_leave(event, button=btn):
            button.configure(bg=COLORS['bg_sidebar'])
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
    
    # إنشاء المحتوى الرئيسي
    global main_content
    main_content = tk.Frame(root, bg=COLORS['bg_main'])
    main_content.pack(side="right", fill="both", expand=True)
    
    # عرض لوحة التحكم افتراضياً
    show_dashboard()

def clear_content():
    """مسح المحتوى الرئيسي"""
    for widget in main_content.winfo_children():
        widget.destroy()

def show_dashboard():
    """عرض لوحة التحكم"""
    clear_content()
    
    # عنوان الصفحة
    tk.Label(main_content, text="لوحة التحكم", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=30)
    
    # رسالة ترحيب
    tk.Label(main_content, text=f"مرحباً بك {current_user['username']}", 
             font=FONTS['heading'], bg=COLORS['bg_main'], fg=COLORS['text_primary']).pack(pady=10)
    
    # إحصائيات
    stats_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    stats_frame.pack(pady=20)
    
    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            
            # عدد المنتجات
            cursor.execute("SELECT COUNT(*) FROM inventory")
            products_count = cursor.fetchone()[0]
            
            # عدد المبيعات
            cursor.execute("SELECT COUNT(*) FROM sales")
            sales_count = cursor.fetchone()[0]
            
            # عدد المستخدمين
            cursor.execute("SELECT COUNT(*) FROM users")
            users_count = cursor.fetchone()[0]
            
            cursor.close()
            db.close()
            
            # عرض الإحصائيات
            tk.Label(stats_frame, text=f"عدد المنتجات: {products_count}", 
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
            tk.Label(stats_frame, text=f"عدد المبيعات: {sales_count}", 
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
            tk.Label(stats_frame, text=f"عدد المستخدمين: {users_count}", 
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
    except:
        tk.Label(stats_frame, text="النظام يعمل بشكل صحيح!", 
                font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['btn_success']).pack(pady=10)

def show_inventory():
    """عرض المخزون الكامل"""
    clear_content()

    # عنوان الصفحة
    title_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    title_frame.pack(fill="x", pady=10)

    tk.Label(title_frame, text="إدارة المخزون", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(side="left", padx=20)

    # زر إضافة منتج جديد
    add_btn = tk.Button(title_frame, text="+ إضافة منتج جديد", command=add_product_window,
                       bg=COLORS['btn_success'], fg=COLORS['text_white'], font=FONTS['button'])
    add_btn.pack(side="right", padx=20)

    # إطار البحث
    search_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    search_frame.pack(fill="x", padx=20, pady=10)

    tk.Label(search_frame, text="البحث:", font=FONTS['main'], bg=COLORS['bg_main']).pack(side="left")
    search_entry = tk.Entry(search_frame, font=FONTS['main'], width=30)
    search_entry.pack(side="left", padx=10)

    search_btn = tk.Button(search_frame, text="بحث", command=lambda: search_products(search_entry.get()),
                          bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    search_btn.pack(side="left", padx=5)

    refresh_btn = tk.Button(search_frame, text="تحديث", command=show_inventory,
                           bg=COLORS['primary'], fg=COLORS['text_white'], font=FONTS['button'])
    refresh_btn.pack(side="left", padx=5)

    # إطار الجدول
    table_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    table_frame.pack(fill="both", expand=True, padx=20, pady=10)

    # إنشاء الجدول
    create_inventory_table(table_frame)

def create_inventory_table(parent):
    """إنشاء جدول المخزون"""
    from tkinter import ttk

    # إنشاء Treeview
    columns = ('اسم المنتج', 'الفئة', 'سعر الشراء', 'سعر البيع', 'الكمية', 'تاريخ الانتهاء')
    tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

    # تعريف العناوين
    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, width=120, anchor='center')

    # إضافة scrollbar
    scrollbar = ttk.Scrollbar(parent, orient="vertical", command=tree.yview)
    tree.configure(yscrollcommand=scrollbar.set)

    # تخطيط الجدول
    tree.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # تحميل البيانات
    load_inventory_data(tree)

    # ربط النقر المزدوج لتعديل المنتج
    tree.bind("<Double-1>", lambda event: edit_product(tree))

    return tree

def load_inventory_data(tree):
    """تحميل بيانات المخزون"""
    # مسح البيانات الموجودة
    for item in tree.get_children():
        tree.delete(item)

    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            cursor.execute("SELECT product_name, category, wholesale_price, selling_price, quantity, expiry_date FROM inventory")
            rows = cursor.fetchall()

            for row in rows:
                # تنسيق تاريخ الانتهاء
                expiry_date = row[5].strftime('%Y-%m-%d') if row[5] else 'غير محدد'
                tree.insert("", "end", values=(row[0], row[1], f"{row[2]:.2f}", f"{row[3]:.2f}", row[4], expiry_date))

            cursor.close()
            db.close()
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تحميل بيانات المخزون: {str(e)}")

def search_products(search_term):
    """البحث في المنتجات"""
    if not search_term.strip():
        show_inventory()
        return

    clear_content()

    # عنوان البحث
    tk.Label(main_content, text=f"نتائج البحث عن: {search_term}",
             font=FONTS['title'], bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=20)

    # زر العودة
    tk.Button(main_content, text="← العودة للمخزون", command=show_inventory,
              bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button']).pack(pady=10)

    # إطار الجدول
    table_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    table_frame.pack(fill="both", expand=True, padx=20, pady=10)

    # إنشاء الجدول
    from tkinter import ttk
    columns = ('اسم المنتج', 'الفئة', 'سعر الشراء', 'سعر البيع', 'الكمية', 'تاريخ الانتهاء')
    tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, width=120, anchor='center')

    scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
    tree.configure(yscrollcommand=scrollbar.set)

    tree.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # البحث في قاعدة البيانات
    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            search_query = f"%{search_term}%"
            cursor.execute("""SELECT product_name, category, wholesale_price, selling_price, quantity, expiry_date
                             FROM inventory WHERE product_name LIKE %s OR category LIKE %s""",
                          (search_query, search_query))
            rows = cursor.fetchall()

            for row in rows:
                expiry_date = row[5].strftime('%Y-%m-%d') if row[5] else 'غير محدد'
                tree.insert("", "end", values=(row[0], row[1], f"{row[2]:.2f}", f"{row[3]:.2f}", row[4], expiry_date))

            cursor.close()
            db.close()

            if not rows:
                tk.Label(main_content, text="لم يتم العثور على نتائج",
                        font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['error']).pack(pady=20)
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")

def add_product_window():
    """نافذة إضافة منتج جديد"""
    add_window = tk.Toplevel(root)
    add_window.title("إضافة منتج جديد - صيدلية الشفاء")
    add_window.geometry("550x650")
    add_window.configure(bg=COLORS['bg_card'])
    add_window.resizable(False, False)

    # توسيط النافذة
    add_window.transient(root)
    add_window.grab_set()

    # توسيط النافذة على الشاشة
    add_window.update_idletasks()
    width = add_window.winfo_width()
    height = add_window.winfo_height()
    x = (add_window.winfo_screenwidth() // 2) - (width // 2)
    y = (add_window.winfo_screenheight() // 2) - (height // 2)
    add_window.geometry(f'{width}x{height}+{x}+{y}')

    # العنوان
    tk.Label(add_window, text="إضافة منتج جديد", font=FONTS['title'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)

    # إطار الحقول
    fields_frame = tk.Frame(add_window, bg=COLORS['bg_card'])
    fields_frame.pack(padx=30, pady=20, fill="both", expand=True)

    # الحقول
    fields = [
        ("اسم المنتج:", "product_name"),
        ("الفئة:", "category"),
        ("سعر الشراء:", "wholesale_price"),
        ("سعر البيع:", "selling_price"),
        ("الكمية:", "quantity"),
        ("تاريخ الانتهاء (YYYY-MM-DD):", "expiry_date")
    ]

    entries = {}
    for label_text, field_name in fields:
        tk.Label(fields_frame, text=label_text, font=FONTS['main'],
                bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))

        entry = tk.Entry(fields_frame, font=FONTS['main'], width=30, relief="solid", bd=1)
        entry.pack(fill="x", pady=(0, 10))
        entries[field_name] = entry

    # إضافة قيم افتراضية مفيدة
    entries['category'].insert(0, "دواء")
    entries['wholesale_price'].insert(0, "0.00")
    entries['selling_price'].insert(0, "0.00")
    entries['quantity'].insert(0, "1")
    entries['expiry_date'].insert(0, "2025-12-31")

    # تركيز على حقل اسم المنتج
    entries['product_name'].focus()

    # مساحة فارغة
    tk.Label(add_window, text="", bg=COLORS['bg_card'], height=1).pack()

    # إطار الأزرار بسيط وواضح
    buttons_frame = tk.Frame(add_window, bg=COLORS['bg_card'])
    buttons_frame.pack(pady=30, padx=30, fill="x")

    # زر إضافة المنتج
    add_btn = tk.Button(buttons_frame,
                       text="إضافة المنتج",
                       command=lambda: save_new_product(entries, add_window),
                       bg="#28a745",
                       fg="white",
                       font=("Arial", 14, "bold"),
                       width=20,
                       height=2,
                       relief="raised",
                       bd=3)
    add_btn.pack(side="left", padx=10, expand=True, fill="x")

    # زر الإلغاء
    cancel_btn = tk.Button(buttons_frame,
                          text="إلغاء",
                          command=add_window.destroy,
                          bg="#dc3545",
                          fg="white",
                          font=("Arial", 14, "bold"),
                          width=20,
                          height=2,
                          relief="raised",
                          bd=3)
    cancel_btn.pack(side="right", padx=10, expand=True, fill="x")

    # ربط مفتاح Enter لحفظ المنتج
    add_window.bind('<Return>', lambda event: save_new_product(entries, add_window))
    add_window.bind('<Escape>', lambda event: add_window.destroy())

    # تأكيد إنشاء الأزرار
    print("✅ تم إنشاء أزرار إضافة المنتج بنجاح")

    # إجبار تحديث النافذة
    add_window.update_idletasks()
    add_window.update()

def save_new_product(entries, window):
    """حفظ منتج جديد"""
    try:
        # التحقق من البيانات
        product_name = entries['product_name'].get().strip()
        category = entries['category'].get().strip()
        wholesale_price = entries['wholesale_price'].get().strip()
        selling_price = entries['selling_price'].get().strip()
        quantity = entries['quantity'].get().strip()
        expiry_date = entries['expiry_date'].get().strip()

        if not all([product_name, category, wholesale_price, selling_price, quantity]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return

        # التحقق من الأرقام
        try:
            wholesale_price = float(wholesale_price)
            selling_price = float(selling_price)
            quantity = int(quantity)

            # التحقق من القيم المنطقية
            if wholesale_price < 0 or selling_price < 0 or quantity < 0:
                messagebox.showerror("خطأ", "لا يمكن أن تكون الأسعار أو الكمية أقل من صفر")
                return

            if selling_price < wholesale_price:
                result = messagebox.askyesno("تحذير", "سعر البيع أقل من سعر الشراء. هل تريد المتابعة؟")
                if not result:
                    return

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للأسعار والكمية")
            return

        # حفظ في قاعدة البيانات
        db = connect_db()
        if db:
            cursor = db.cursor()

            # التحقق من عدم وجود منتج بنفس الاسم
            cursor.execute("SELECT id FROM inventory WHERE product_name = %s", (product_name,))
            if cursor.fetchone():
                messagebox.showerror("خطأ", "يوجد منتج بنفس الاسم مسبقاً")
                cursor.close()
                db.close()
                return

            # إدراج المنتج الجديد
            query = """INSERT INTO inventory (product_name, category, wholesale_price, selling_price, quantity, expiry_date)
                      VALUES (%s, %s, %s, %s, %s, %s)"""

            expiry_date_value = expiry_date if expiry_date else None
            cursor.execute(query, (product_name, category, wholesale_price, selling_price, quantity, expiry_date_value))

            db.commit()
            cursor.close()
            db.close()

            messagebox.showinfo("✅ تم بنجاح", f"تم إضافة المنتج '{product_name}' بنجاح!\n\nالكمية: {quantity}\nسعر البيع: {selling_price} أوقية")
            window.destroy()
            show_inventory()  # تحديث المخزون

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في إضافة المنتج: {str(e)}")

def edit_product(tree):
    """تعديل منتج محدد"""
    selected = tree.selection()
    if not selected:
        messagebox.showwarning("تحذير", "يرجى اختيار منتج للتعديل")
        return

    # الحصول على بيانات المنتج المحدد
    item = tree.item(selected[0])
    values = item['values']

    # إنشاء نافذة التعديل
    edit_window = tk.Toplevel(root)
    edit_window.title("تعديل المنتج")
    edit_window.geometry("400x500")
    edit_window.configure(bg=COLORS['bg_card'])
    edit_window.resizable(False, False)

    edit_window.transient(root)
    edit_window.grab_set()

    # العنوان
    tk.Label(edit_window, text="تعديل المنتج", font=FONTS['title'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)

    # إطار الحقول
    fields_frame = tk.Frame(edit_window, bg=COLORS['bg_card'])
    fields_frame.pack(padx=30, pady=20, fill="both", expand=True)

    # الحقول مع القيم الحالية
    fields = [
        ("اسم المنتج:", "product_name", values[0]),
        ("الفئة:", "category", values[1]),
        ("سعر الشراء:", "wholesale_price", values[2]),
        ("سعر البيع:", "selling_price", values[3]),
        ("الكمية:", "quantity", values[4]),
        ("تاريخ الانتهاء (YYYY-MM-DD):", "expiry_date", values[5])
    ]

    entries = {}
    for label_text, field_name, current_value in fields:
        tk.Label(fields_frame, text=label_text, font=FONTS['main'],
                bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))

        entry = tk.Entry(fields_frame, font=FONTS['main'], width=30, relief="solid", bd=1)
        entry.pack(fill="x", pady=(0, 10))
        entry.insert(0, str(current_value))
        entries[field_name] = entry

    # إطار الأزرار
    buttons_frame = tk.Frame(edit_window, bg=COLORS['bg_card'])
    buttons_frame.pack(pady=20)

    # زر الحفظ
    save_btn = tk.Button(buttons_frame, text="حفظ التغييرات",
                        command=lambda: update_product(entries, values[0], edit_window),
                        bg=COLORS['btn_success'], fg=COLORS['text_white'],
                        font=FONTS['button'], width=15)
    save_btn.pack(side="left", padx=10)

    # زر الحذف
    delete_btn = tk.Button(buttons_frame, text="حذف المنتج",
                          command=lambda: delete_product(values[0], edit_window),
                          bg=COLORS['error'], fg=COLORS['text_white'],
                          font=FONTS['button'], width=15)
    delete_btn.pack(side="left", padx=10)

    # زر الإلغاء
    cancel_btn = tk.Button(buttons_frame, text="إلغاء", command=edit_window.destroy,
                          bg=COLORS['accent'], fg=COLORS['text_white'],
                          font=FONTS['button'], width=15)
    cancel_btn.pack(side="left", padx=10)

def update_product(entries, original_name, window):
    """تحديث بيانات المنتج"""
    try:
        # الحصول على البيانات الجديدة
        product_name = entries['product_name'].get().strip()
        category = entries['category'].get().strip()
        wholesale_price = entries['wholesale_price'].get().strip()
        selling_price = entries['selling_price'].get().strip()
        quantity = entries['quantity'].get().strip()
        expiry_date = entries['expiry_date'].get().strip()

        if not all([product_name, category, wholesale_price, selling_price, quantity]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return

        # التحقق من الأرقام
        try:
            wholesale_price = float(wholesale_price)
            selling_price = float(selling_price)
            quantity = int(quantity)
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للأسعار والكمية")
            return

        # تحديث في قاعدة البيانات
        db = connect_db()
        if db:
            cursor = db.cursor()

            query = """UPDATE inventory SET product_name = %s, category = %s, wholesale_price = %s,
                      selling_price = %s, quantity = %s, expiry_date = %s WHERE product_name = %s"""

            expiry_date_value = expiry_date if expiry_date and expiry_date != 'غير محدد' else None
            cursor.execute(query, (product_name, category, wholesale_price, selling_price,
                                 quantity, expiry_date_value, original_name))

            db.commit()
            cursor.close()
            db.close()

            messagebox.showinfo("نجح", "تم تحديث المنتج بنجاح!")
            window.destroy()
            show_inventory()  # تحديث المخزون

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تحديث المنتج: {str(e)}")

def delete_product(product_name, window):
    """حذف منتج"""
    result = messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف المنتج '{product_name}' نهائياً؟")
    if result:
        try:
            db = connect_db()
            if db:
                cursor = db.cursor()
                cursor.execute("DELETE FROM inventory WHERE product_name = %s", (product_name,))
                db.commit()
                cursor.close()
                db.close()

                messagebox.showinfo("نجح", "تم حذف المنتج بنجاح!")
                window.destroy()
                show_inventory()  # تحديث المخزون

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف المنتج: {str(e)}")

def show_sales():
    """عرض نظام المبيعات الكامل"""
    clear_content()

    # عنوان الصفحة
    title_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    title_frame.pack(fill="x", pady=10)

    tk.Label(title_frame, text="نظام المبيعات", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(side="left", padx=20)

    # زر عرض تقرير المبيعات
    report_btn = tk.Button(title_frame, text="📊 تقرير المبيعات", command=show_sales_report,
                          bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    report_btn.pack(side="right", padx=20)

    # إطار رئيسي مقسم مع تحسين الحجم
    main_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    main_frame.pack(fill="both", expand=True, padx=10, pady=5)

    # الجانب الأيسر - اختيار المنتجات (أصغر)
    left_frame = tk.Frame(main_frame, bg=COLORS['bg_card'], relief="raised", bd=2)
    left_frame.pack(side="left", fill="both", expand=True, padx=(0, 5))

    # الجانب الأيمن - السلة والدفع (أكبر)
    right_frame = tk.Frame(main_frame, bg=COLORS['bg_card'], relief="raised", bd=2)
    right_frame.pack(side="right", fill="both", expand=True, padx=(5, 0))

    # إنشاء واجهة اختيار المنتجات
    create_product_selection(left_frame)

    # إنشاء واجهة السلة والدفع
    create_cart_and_payment(right_frame)

def create_product_selection(parent):
    """إنشاء واجهة اختيار المنتجات"""
    # عنوان القسم
    tk.Label(parent, text="اختيار المنتجات", font=FONTS['heading'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=10)

    # إطار البحث
    search_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    search_frame.pack(fill="x", padx=10, pady=5)

    tk.Label(search_frame, text="البحث:", font=FONTS['main'], bg=COLORS['bg_card']).pack(side="left")
    global product_search_entry
    product_search_entry = tk.Entry(search_frame, font=FONTS['main'], width=20)
    product_search_entry.pack(side="left", padx=5)

    search_btn = tk.Button(search_frame, text="بحث", command=filter_products,
                          bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    search_btn.pack(side="left", padx=5)

    # قائمة المنتجات
    products_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    products_frame.pack(fill="both", expand=True, padx=10, pady=10)

    # إنشاء جدول المنتجات (أصغر)
    from tkinter import ttk
    global products_tree
    columns = ('المنتج', 'السعر', 'المتاح')
    products_tree = ttk.Treeview(products_frame, columns=columns, show='headings', height=8)

    for col in columns:
        products_tree.heading(col, text=col)
        products_tree.column(col, width=100, anchor='center')

    # scrollbar للمنتجات
    products_scrollbar = ttk.Scrollbar(products_frame, orient="vertical", command=products_tree.yview)
    products_tree.configure(yscrollcommand=products_scrollbar.set)

    products_tree.pack(side="left", fill="both", expand=True)
    products_scrollbar.pack(side="right", fill="y")

    # تحميل المنتجات
    load_products_for_sale()

    # إطار إضافة للسلة
    add_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    add_frame.pack(fill="x", padx=10, pady=10)

    tk.Label(add_frame, text="الكمية:", font=FONTS['main'], bg=COLORS['bg_card']).pack(side="left")
    global quantity_entry
    quantity_entry = tk.Entry(add_frame, font=FONTS['main'], width=10)
    quantity_entry.pack(side="left", padx=5)
    quantity_entry.insert(0, "1")

    add_to_cart_btn = tk.Button(add_frame, text="إضافة للسلة", command=add_to_cart,
                               bg=COLORS['btn_success'], fg=COLORS['text_white'], font=FONTS['button'])
    add_to_cart_btn.pack(side="left", padx=10)

def create_cart_and_payment(parent):
    """إنشاء واجهة السلة والدفع"""
    # عنوان القسم
    tk.Label(parent, text="السلة والدفع", font=FONTS['heading'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=10)

    # السلة
    cart_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    cart_frame.pack(fill="both", expand=True, padx=10, pady=5)

    from tkinter import ttk
    global cart_tree
    cart_columns = ('المنتج', 'السعر', 'الكمية', 'الإجمالي')
    cart_tree = ttk.Treeview(cart_frame, columns=cart_columns, show='headings', height=5)

    for col in cart_columns:
        cart_tree.heading(col, text=col)
        cart_tree.column(col, width=80, anchor='center')

    cart_scrollbar = ttk.Scrollbar(cart_frame, orient="vertical", command=cart_tree.yview)
    cart_tree.configure(yscrollcommand=cart_scrollbar.set)

    cart_tree.pack(side="left", fill="both", expand=True)
    cart_scrollbar.pack(side="right", fill="y")

    # أزرار السلة
    cart_buttons_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    cart_buttons_frame.pack(fill="x", padx=10, pady=5)

    remove_btn = tk.Button(cart_buttons_frame, text="حذف من السلة", command=remove_from_cart,
                          bg=COLORS['error'], fg=COLORS['text_white'], font=FONTS['button'])
    remove_btn.pack(side="left", padx=5)

    clear_btn = tk.Button(cart_buttons_frame, text="مسح السلة", command=clear_cart,
                         bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    clear_btn.pack(side="left", padx=5)

    # الإجمالي
    total_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    total_frame.pack(fill="x", padx=10, pady=10)

    global total_label
    total_label = tk.Label(total_frame, text="الإجمالي: 0.00", font=FONTS['heading'],
                          bg=COLORS['bg_card'], fg=COLORS['primary'])
    total_label.pack()

    # زر إتمام البيع والطباعة المباشرة
    complete_sale_btn = tk.Button(total_frame,
                                 text="� إتمام البيع وطباعة الفاتورة",
                                 command=complete_sale_with_print,
                                 bg="#28a745",
                                 fg="white",
                                 font=("Arial", 12, "bold"),
                                 width=35,
                                 height=2,
                                 relief="raised",
                                 bd=3)
    complete_sale_btn.pack(pady=10)

    # معلومات العميل
    customer_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    customer_frame.pack(fill="x", padx=10, pady=5)

    tk.Label(customer_frame, text="اسم العميل:", font=FONTS['main'], bg=COLORS['bg_card']).pack(anchor="w")
    global customer_entry
    customer_entry = tk.Entry(customer_frame, font=FONTS['main'], width=25)
    customer_entry.pack(fill="x", pady=2)

    tk.Label(customer_frame, text="رقم الهاتف:", font=FONTS['main'], bg=COLORS['bg_card']).pack(anchor="w")
    global phone_entry
    phone_entry = tk.Entry(customer_frame, font=FONTS['main'], width=25)
    phone_entry.pack(fill="x", pady=2)

    # طريقة الدفع (مضغوطة)
    payment_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    payment_frame.pack(fill="x", padx=10, pady=3)

    tk.Label(payment_frame, text="طريقة الدفع:", font=("Arial", 10, "bold"),
             bg=COLORS['bg_card']).pack(anchor="w")

    global payment_var
    payment_var = tk.StringVar(value="نقد")

    # تقسيم خيارات الدفع في صفين
    payment_methods = ["نقد", "بنكلي", "مصرفي", "سداد", "BIC Bank", "Click"]

    # الصف الأول
    row1_frame = tk.Frame(payment_frame, bg=COLORS['bg_card'])
    row1_frame.pack(fill="x", pady=2)

    for method in payment_methods[:3]:
        tk.Radiobutton(row1_frame, text=method, variable=payment_var, value=method,
                      bg=COLORS['bg_card'], font=("Arial", 9)).pack(side="left", padx=5)

    # الصف الثاني
    row2_frame = tk.Frame(payment_frame, bg=COLORS['bg_card'])
    row2_frame.pack(fill="x", pady=2)

    for method in payment_methods[3:]:
        tk.Radiobutton(row2_frame, text=method, variable=payment_var, value=method,
                      bg=COLORS['bg_card'], font=("Arial", 9)).pack(side="left", padx=5)

    # تأكيد إنشاء زر إتمام البيع
    print("✅ تم إنشاء زر إتمام البيع والطباعة المباشرة بنجاح")

# متغيرات السلة والفاتورة
cart_items = []
last_invoice_data = None  # لحفظ بيانات آخر فاتورة

def load_products_for_sale():
    """تحميل المنتجات المتاحة للبيع"""
    try:
        # مسح البيانات الموجودة
        for item in products_tree.get_children():
            products_tree.delete(item)

        db = connect_db()
        if db:
            cursor = db.cursor()
            cursor.execute("SELECT product_name, selling_price, quantity FROM inventory WHERE quantity > 0")
            rows = cursor.fetchall()

            for row in rows:
                products_tree.insert("", "end", values=(row[0], f"{row[1]:.2f}", row[2]))

            cursor.close()
            db.close()
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تحميل المنتجات: {str(e)}")

def filter_products():
    """فلترة المنتجات حسب البحث"""
    search_term = product_search_entry.get().strip()

    try:
        # مسح البيانات الموجودة
        for item in products_tree.get_children():
            products_tree.delete(item)

        db = connect_db()
        if db:
            cursor = db.cursor()
            if search_term:
                search_query = f"%{search_term}%"
                cursor.execute("SELECT product_name, selling_price, quantity FROM inventory WHERE quantity > 0 AND product_name LIKE %s", (search_query,))
            else:
                cursor.execute("SELECT product_name, selling_price, quantity FROM inventory WHERE quantity > 0")

            rows = cursor.fetchall()

            for row in rows:
                products_tree.insert("", "end", values=(row[0], f"{row[1]:.2f}", row[2]))

            cursor.close()
            db.close()
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")

def add_to_cart():
    """إضافة منتج للسلة"""
    selected = products_tree.selection()
    if not selected:
        messagebox.showwarning("تحذير", "يرجى اختيار منتج")
        return

    try:
        quantity = int(quantity_entry.get().strip())
        if quantity <= 0:
            messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
            return
    except ValueError:
        messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح للكمية")
        return

    # الحصول على بيانات المنتج
    item = products_tree.item(selected[0])
    values = item['values']
    product_name = values[0]
    price = float(values[1])
    available = int(values[2])

    if quantity > available:
        messagebox.showerror("خطأ", f"الكمية المطلوبة ({quantity}) أكبر من المتاح ({available})")
        return

    # التحقق من وجود المنتج في السلة
    for i, cart_item in enumerate(cart_items):
        if cart_item[0] == product_name:
            # تحديث الكمية
            new_quantity = cart_item[2] + quantity
            if new_quantity > available:
                messagebox.showerror("خطأ", f"إجمالي الكمية ({new_quantity}) أكبر من المتاح ({available})")
                return
            cart_items[i] = (product_name, price, new_quantity, price * new_quantity)
            break
    else:
        # إضافة منتج جديد للسلة
        total = price * quantity
        cart_items.append((product_name, price, quantity, total))

    # تحديث عرض السلة
    update_cart_display()

    # مسح حقل الكمية
    quantity_entry.delete(0, tk.END)
    quantity_entry.insert(0, "1")

def update_cart_display():
    """تحديث عرض السلة"""
    # مسح السلة الحالية
    for item in cart_tree.get_children():
        cart_tree.delete(item)

    # إضافة العناصر
    total_amount = 0
    for item in cart_items:
        cart_tree.insert("", "end", values=(item[0], f"{item[1]:.2f}", item[2], f"{item[3]:.2f}"))
        total_amount += item[3]

    # تحديث الإجمالي
    total_label.config(text=f"الإجمالي: {total_amount:.2f}")

def remove_from_cart():
    """حذف منتج من السلة"""
    selected = cart_tree.selection()
    if not selected:
        messagebox.showwarning("تحذير", "يرجى اختيار منتج من السلة")
        return

    # الحصول على اسم المنتج
    item = cart_tree.item(selected[0])
    product_name = item['values'][0]

    # حذف من قائمة السلة
    cart_items[:] = [item for item in cart_items if item[0] != product_name]

    # تحديث العرض
    update_cart_display()

def clear_cart():
    """مسح السلة بالكامل"""
    result = messagebox.askyesno("تأكيد", "هل تريد مسح جميع عناصر السلة؟")
    if result:
        cart_items.clear()
        update_cart_display()

def complete_sale():
    """إتمام البيع"""
    if not cart_items:
        messagebox.showwarning("تحذير", "السلة فارغة")
        return

    customer_name = customer_entry.get().strip()
    phone = phone_entry.get().strip()
    payment_method = payment_var.get()

    if not customer_name:
        customer_name = "عميل"

    try:
        db = connect_db()
        if db:
            cursor = db.cursor()

            # إنشاء رقم فاتورة
            from datetime import datetime
            now = datetime.now()
            invoice_id = f"INV-{now.strftime('%Y%m%d%H%M%S')}"

            # حفظ المبيعات وتحديث المخزون
            for item in cart_items:
                product_name, price, quantity, total = item

                # إدراج في جدول المبيعات
                cursor.execute("""INSERT INTO sales (invoice_id, product_name, price, qty, total, time, payment_method, customer, phone, date)
                                 VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                              (invoice_id, product_name, price, quantity, total, now.strftime("%H:%M:%S"),
                               payment_method, customer_name, phone, now.strftime("%Y-%m-%d")))

                # تحديث المخزون
                cursor.execute("UPDATE inventory SET quantity = quantity - %s WHERE product_name = %s",
                              (quantity, product_name))

            db.commit()
            cursor.close()
            db.close()

            # عرض رسالة نجاح مفصلة
            total_amount = sum(item[3] for item in cart_items)
            success_message = f"""✅ تم إتمام البيع بنجاح!

📋 رقم الفاتورة: {invoice_id}
👤 العميل: {customer_name}
📞 الهاتف: {phone if phone else 'غير محدد'}
💳 طريقة الدفع: {payment_method}
💰 الإجمالي: {total_amount:.2f} أوقية

عدد المنتجات: {len(cart_items)}"""

            messagebox.showinfo("🎉 تم بنجاح", success_message)

            # طباعة الفاتورة
            print_invoice(invoice_id, cart_items, customer_name, phone, payment_method, total_amount)

            # مسح السلة وإعادة تعيين الحقول
            cart_items.clear()
            update_cart_display()
            customer_entry.delete(0, tk.END)
            phone_entry.delete(0, tk.END)
            payment_var.set("نقد")

            # تحديث قائمة المنتجات لإظهار الكميات الجديدة
            load_products_for_sale()
            customer_entry.delete(0, tk.END)
            phone_entry.delete(0, tk.END)
            load_products_for_sale()  # تحديث المنتجات المتاحة

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في إتمام البيع: {str(e)}")

def print_invoice(invoice_id, items, customer, phone, payment_method, total):
    """طباعة الفاتورة (محاكاة)"""
    print("\n" + "="*50)
    print("           صيدلية الشفاء")
    print("         فاتورة مبيعات")
    print("="*50)
    print(f"رقم الفاتورة: {invoice_id}")
    print(f"العميل: {customer}")
    print(f"الهاتف: {phone}")
    print(f"طريقة الدفع: {payment_method}")
    print(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-"*50)
    print(f"{'المنتج':<20} {'السعر':<10} {'الكمية':<8} {'الإجمالي':<10}")
    print("-"*50)

    for item in items:
        print(f"{item[0]:<20} {item[1]:<10.2f} {item[2]:<8} {item[3]:<10.2f}")

    print("-"*50)
    print(f"{'الإجمالي النهائي:':<39} {total:<10.2f}")
    print("="*50)
    print("شكراً لتعاملكم معنا")
    print("="*50)

def complete_sale_with_print():
    """إتمام البيع مع الطباعة المباشرة"""
    if not cart_items:
        messagebox.showwarning("تحذير", "السلة فارغة")
        return

    customer_name = customer_entry.get().strip()
    phone = phone_entry.get().strip()
    payment_method = payment_var.get()

    if not customer_name:
        customer_name = "عميل"

    try:
        db = connect_db()
        if db:
            cursor = db.cursor()

            # إنشاء رقم فاتورة
            from datetime import datetime
            now = datetime.now()
            invoice_id = f"INV-{now.strftime('%Y%m%d%H%M%S')}"

            # حساب الإجمالي
            total_amount = sum(item[3] for item in cart_items)

            # حفظ المبيعات وتحديث المخزون
            for item in cart_items:
                product_name, price, quantity, total = item

                # إدراج في جدول المبيعات
                cursor.execute("""INSERT INTO sales (invoice_id, product_name, price, qty, total, time, payment_method, customer, phone, date)
                                 VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                              (invoice_id, product_name, price, quantity, total, now.strftime("%H:%M:%S"),
                               payment_method, customer_name, phone, now.strftime("%Y-%m-%d")))

                # تحديث المخزون
                cursor.execute("UPDATE inventory SET quantity = quantity - %s WHERE product_name = %s",
                              (quantity, product_name))

            db.commit()
            cursor.close()
            db.close()

            # طباعة الفاتورة مباشرة
            print_invoice_detailed({
                'invoice_id': invoice_id,
                'items': cart_items.copy(),
                'customer': customer_name,
                'phone': phone,
                'payment_method': payment_method,
                'total': total_amount,
                'date': now.strftime("%Y-%m-%d"),
                'time': now.strftime("%H:%M:%S")
            })

            # عرض رسالة نجاح
            success_message = f"""✅ تم إتمام البيع والطباعة بنجاح!

📋 رقم الفاتورة: {invoice_id}
👤 العميل: {customer_name}
📞 الهاتف: {phone if phone else 'غير محدد'}
💳 طريقة الدفع: {payment_method}
💰 الإجمالي: {total_amount:.2f} أوقية

عدد المنتجات: {len(cart_items)}

🖨️ تم طباعة الفاتورة في الطرفية"""

            messagebox.showinfo("🎉 تم بنجاح", success_message)

            # مسح السلة وإعادة تعيين الحقول
            cart_items.clear()
            update_cart_display()
            customer_entry.delete(0, tk.END)
            phone_entry.delete(0, tk.END)
            payment_var.set("نقد")

            # تحديث قائمة المنتجات لإظهار الكميات الجديدة
            load_products_for_sale()

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في إتمام البيع: {str(e)}")

def complete_sale_and_enable_print():
    """إتمام البيع وتفعيل زر الطباعة"""
    global last_invoice_data

    if not cart_items:
        messagebox.showwarning("تحذير", "السلة فارغة")
        return

    customer_name = customer_entry.get().strip()
    phone = phone_entry.get().strip()
    payment_method = payment_var.get()

    if not customer_name:
        customer_name = "عميل"

    try:
        db = connect_db()
        if db:
            cursor = db.cursor()

            # إنشاء رقم فاتورة
            from datetime import datetime
            now = datetime.now()
            invoice_id = f"INV-{now.strftime('%Y%m%d%H%M%S')}"

            # حفظ بيانات الفاتورة للطباعة
            total_amount = sum(item[3] for item in cart_items)
            last_invoice_data = {
                'invoice_id': invoice_id,
                'items': cart_items.copy(),
                'customer': customer_name,
                'phone': phone,
                'payment_method': payment_method,
                'total': total_amount,
                'date': now.strftime("%Y-%m-%d"),
                'time': now.strftime("%H:%M:%S")
            }

            # حفظ المبيعات وتحديث المخزون
            for item in cart_items:
                product_name, price, quantity, total = item

                # إدراج في جدول المبيعات
                cursor.execute("""INSERT INTO sales (invoice_id, product_name, price, qty, total, time, payment_method, customer, phone, date)
                                 VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                              (invoice_id, product_name, price, quantity, total, now.strftime("%H:%M:%S"),
                               payment_method, customer_name, phone, now.strftime("%Y-%m-%d")))

                # تحديث المخزون
                cursor.execute("UPDATE inventory SET quantity = quantity - %s WHERE product_name = %s",
                              (quantity, product_name))

            db.commit()
            cursor.close()
            db.close()

            # عرض رسالة نجاح
            success_message = f"""✅ تم إتمام البيع بنجاح!

📋 رقم الفاتورة: {invoice_id}
👤 العميل: {customer_name}
📞 الهاتف: {phone if phone else 'غير محدد'}
💳 طريقة الدفع: {payment_method}
💰 الإجمالي: {total_amount:.2f} أوقية

عدد المنتجات: {len(cart_items)}

✨ يمكنك الآن طباعة الفاتورة من الزر الأزرق"""

            messagebox.showinfo("🎉 تم بنجاح", success_message)

            # لا حاجة لتفعيل أزرار إضافية - الطباعة تمت مباشرة

            # مسح السلة وإعادة تعيين الحقول
            cart_items.clear()
            update_cart_display()
            customer_entry.delete(0, tk.END)
            phone_entry.delete(0, tk.END)
            payment_var.set("نقد")

            # تحديث قائمة المنتجات لإظهار الكميات الجديدة
            load_products_for_sale()

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في إتمام البيع: {str(e)}")



def print_invoice_detailed(invoice_data):
    """طباعة فاتورة مفصلة"""
    print("\n" + "="*60)
    print("                    🏥 صيدلية الشفاء")
    print("                   📋 فاتورة مبيعات")
    print("="*60)
    print(f"📋 رقم الفاتورة: {invoice_data['invoice_id']}")
    print(f"📅 التاريخ: {invoice_data['date']}")
    print(f"🕐 الوقت: {invoice_data['time']}")
    print(f"👤 العميل: {invoice_data['customer']}")
    print(f"📞 الهاتف: {invoice_data['phone'] if invoice_data['phone'] else 'غير محدد'}")
    print(f"💳 طريقة الدفع: {invoice_data['payment_method']}")
    print("-"*60)
    print(f"{'المنتج':<25} {'السعر':<10} {'الكمية':<8} {'الإجمالي':<10}")
    print("-"*60)

    for item in invoice_data['items']:
        product_name, price, quantity, total = item
        print(f"{product_name:<25} {price:<10.2f} {quantity:<8} {total:<10.2f}")

    print("-"*60)
    print(f"{'الإجمالي النهائي:':<44} {invoice_data['total']:<10.2f} أوقية")
    print("="*60)
    print("                  🙏 شكراً لتعاملكم معنا")
    print("                   💊 دواؤكم أمانة عندنا")
    print("="*60)

def show_sales_report():
    """عرض تقرير المبيعات"""
    clear_content()

    # عنوان التقرير
    tk.Label(main_content, text="تقرير المبيعات", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=20)

    # زر العودة
    tk.Button(main_content, text="← العودة للمبيعات", command=show_sales,
              bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button']).pack(pady=10)

    # إطار الإحصائيات
    stats_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    stats_frame.pack(pady=20)

    try:
        db = connect_db()
        if db:
            cursor = db.cursor()

            # إحصائيات اليوم
            cursor.execute("SELECT COUNT(DISTINCT invoice_id), SUM(total) FROM sales WHERE DATE(date) = CURDATE()")
            today_data = cursor.fetchone()
            today_invoices = today_data[0] or 0
            today_total = today_data[1] or 0

            # إحصائيات الشهر
            cursor.execute("SELECT COUNT(DISTINCT invoice_id), SUM(total) FROM sales WHERE MONTH(date) = MONTH(CURDATE()) AND YEAR(date) = YEAR(CURDATE())")
            month_data = cursor.fetchone()
            month_invoices = month_data[0] or 0
            month_total = month_data[1] or 0

            # إحصائيات إجمالية
            cursor.execute("SELECT COUNT(DISTINCT invoice_id), SUM(total) FROM sales")
            total_data = cursor.fetchone()
            total_invoices = total_data[0] or 0
            total_sales = total_data[1] or 0

            cursor.close()
            db.close()

            # عرض الإحصائيات
            tk.Label(stats_frame, text=f"مبيعات اليوم: {today_invoices} فاتورة - {today_total:.2f}",
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
            tk.Label(stats_frame, text=f"مبيعات الشهر: {month_invoices} فاتورة - {month_total:.2f}",
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
            tk.Label(stats_frame, text=f"إجمالي المبيعات: {total_invoices} فاتورة - {total_sales:.2f}",
                    font=FONTS['heading'], bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=10)

    except Exception as e:
        tk.Label(stats_frame, text=f"خطأ في تحميل التقرير: {str(e)}",
                font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['error']).pack(pady=10)

def show_invoices():
    """عرض إدارة الفواتير الكاملة"""
    clear_content()

    # عنوان الصفحة
    title_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    title_frame.pack(fill="x", pady=10)

    tk.Label(title_frame, text="إدارة الفواتير", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(side="left", padx=20)

    # زر طباعة تقرير شامل
    print_report_btn = tk.Button(title_frame, text="🖨️ طباعة تقرير شامل", command=print_all_invoices_report,
                                bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    print_report_btn.pack(side="right", padx=20)

    # إطار البحث والفلترة
    search_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    search_frame.pack(fill="x", padx=20, pady=10)

    # البحث برقم الفاتورة
    tk.Label(search_frame, text="رقم الفاتورة:", font=FONTS['main'], bg=COLORS['bg_main']).pack(side="left")
    global invoice_search_entry
    invoice_search_entry = tk.Entry(search_frame, font=FONTS['main'], width=20)
    invoice_search_entry.pack(side="left", padx=5)

    # البحث بالعميل
    tk.Label(search_frame, text="العميل:", font=FONTS['main'], bg=COLORS['bg_main']).pack(side="left", padx=(20, 5))
    global customer_search_entry
    customer_search_entry = tk.Entry(search_frame, font=FONTS['main'], width=15)
    customer_search_entry.pack(side="left", padx=5)

    # البحث بالتاريخ
    tk.Label(search_frame, text="التاريخ:", font=FONTS['main'], bg=COLORS['bg_main']).pack(side="left", padx=(20, 5))
    global date_search_entry
    date_search_entry = tk.Entry(search_frame, font=FONTS['main'], width=12)
    date_search_entry.pack(side="left", padx=5)
    date_search_entry.insert(0, "YYYY-MM-DD")

    # أزرار البحث
    search_btn = tk.Button(search_frame, text="بحث", command=search_invoices,
                          bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    search_btn.pack(side="left", padx=10)

    refresh_btn = tk.Button(search_frame, text="عرض الكل", command=show_invoices,
                           bg=COLORS['primary'], fg=COLORS['text_white'], font=FONTS['button'])
    refresh_btn.pack(side="left", padx=5)

    # إطار الجدول
    table_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    table_frame.pack(fill="both", expand=True, padx=20, pady=10)

    # إنشاء جدول الفواتير
    create_invoices_table(table_frame)

def create_invoices_table(parent):
    """إنشاء جدول الفواتير"""
    from tkinter import ttk

    # إنشاء Treeview
    columns = ('رقم الفاتورة', 'العميل', 'الهاتف', 'الإجمالي', 'طريقة الدفع', 'التاريخ', 'الوقت')
    global invoices_tree
    invoices_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

    # تعريف العناوين والعرض
    column_widths = {'رقم الفاتورة': 150, 'العميل': 120, 'الهاتف': 100, 'الإجمالي': 80,
                    'طريقة الدفع': 100, 'التاريخ': 100, 'الوقت': 80}

    for col in columns:
        invoices_tree.heading(col, text=col)
        invoices_tree.column(col, width=column_widths.get(col, 100), anchor='center')

    # إضافة scrollbar
    scrollbar = ttk.Scrollbar(parent, orient="vertical", command=invoices_tree.yview)
    invoices_tree.configure(yscrollcommand=scrollbar.set)

    # تخطيط الجدول
    invoices_tree.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # تحميل البيانات
    load_invoices_data()

    # ربط النقر المزدوج لعرض تفاصيل الفاتورة
    invoices_tree.bind("<Double-1>", lambda event: show_invoice_details())

    return invoices_tree

def load_invoices_data():
    """تحميل بيانات الفواتير"""
    try:
        # مسح البيانات الموجودة
        for item in invoices_tree.get_children():
            invoices_tree.delete(item)

        db = connect_db()
        if db:
            cursor = db.cursor()
            # استعلام لجمع بيانات الفواتير
            query = """
            SELECT invoice_id, customer, phone, SUM(total) as total_amount,
                   payment_method, date, time
            FROM sales
            GROUP BY invoice_id, customer, phone, payment_method, date, time
            ORDER BY date DESC, time DESC
            """
            cursor.execute(query)
            rows = cursor.fetchall()

            for row in rows:
                invoices_tree.insert("", "end", values=(
                    row[0], row[1] or 'عميل', row[2] or '-',
                    f"{row[3]:.2f}", row[4], row[5], row[6]
                ))

            cursor.close()
            db.close()

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تحميل الفواتير: {str(e)}")

def search_invoices():
    """البحث في الفواتير"""
    invoice_id = invoice_search_entry.get().strip()
    customer = customer_search_entry.get().strip()
    date = date_search_entry.get().strip()

    if date == "YYYY-MM-DD":
        date = ""

    try:
        # مسح البيانات الموجودة
        for item in invoices_tree.get_children():
            invoices_tree.delete(item)

        db = connect_db()
        if db:
            cursor = db.cursor()

            # بناء الاستعلام حسب معايير البحث
            conditions = []
            params = []

            if invoice_id:
                conditions.append("invoice_id LIKE %s")
                params.append(f"%{invoice_id}%")

            if customer:
                conditions.append("customer LIKE %s")
                params.append(f"%{customer}%")

            if date:
                conditions.append("date = %s")
                params.append(date)

            where_clause = " AND ".join(conditions) if conditions else "1=1"

            query = f"""
            SELECT invoice_id, customer, phone, SUM(total) as total_amount,
                   payment_method, date, time
            FROM sales
            WHERE {where_clause}
            GROUP BY invoice_id, customer, phone, payment_method, date, time
            ORDER BY date DESC, time DESC
            """

            cursor.execute(query, params)
            rows = cursor.fetchall()

            for row in rows:
                invoices_tree.insert("", "end", values=(
                    row[0], row[1] or 'عميل', row[2] or '-',
                    f"{row[3]:.2f}", row[4], row[5], row[6]
                ))

            cursor.close()
            db.close()

            if not rows:
                messagebox.showinfo("نتيجة البحث", "لم يتم العثور على فواتير تطابق معايير البحث")

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")

def show_invoice_details():
    """عرض تفاصيل الفاتورة"""
    selected = invoices_tree.selection()
    if not selected:
        messagebox.showwarning("تحذير", "يرجى اختيار فاتورة لعرض تفاصيلها")
        return

    # الحصول على رقم الفاتورة
    item = invoices_tree.item(selected[0])
    invoice_id = item['values'][0]

    # إنشاء نافذة التفاصيل
    details_window = tk.Toplevel(root)
    details_window.title(f"تفاصيل الفاتورة - {invoice_id}")
    details_window.geometry("600x500")
    details_window.configure(bg=COLORS['bg_card'])
    details_window.resizable(False, False)

    details_window.transient(root)
    details_window.grab_set()

    # العنوان
    tk.Label(details_window, text=f"تفاصيل الفاتورة: {invoice_id}",
             font=FONTS['title'], bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)

    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            cursor.execute("SELECT * FROM sales WHERE invoice_id = %s", (invoice_id,))
            rows = cursor.fetchall()

            if rows:
                # معلومات الفاتورة
                first_row = rows[0]
                info_frame = tk.Frame(details_window, bg=COLORS['bg_card'])
                info_frame.pack(fill="x", padx=20, pady=10)

                tk.Label(info_frame, text=f"العميل: {first_row[7] or 'عميل'}",
                        font=FONTS['main'], bg=COLORS['bg_card']).pack(anchor="w")
                tk.Label(info_frame, text=f"الهاتف: {first_row[8] or '-'}",
                        font=FONTS['main'], bg=COLORS['bg_card']).pack(anchor="w")
                tk.Label(info_frame, text=f"طريقة الدفع: {first_row[6]}",
                        font=FONTS['main'], bg=COLORS['bg_card']).pack(anchor="w")
                tk.Label(info_frame, text=f"التاريخ: {first_row[9]} - الوقت: {first_row[5]}",
                        font=FONTS['main'], bg=COLORS['bg_card']).pack(anchor="w")

                # جدول المنتجات
                from tkinter import ttk
                products_frame = tk.Frame(details_window, bg=COLORS['bg_card'])
                products_frame.pack(fill="both", expand=True, padx=20, pady=10)

                columns = ('المنتج', 'السعر', 'الكمية', 'الإجمالي')
                tree = ttk.Treeview(products_frame, columns=columns, show='headings', height=10)

                for col in columns:
                    tree.heading(col, text=col)
                    tree.column(col, width=120, anchor='center')

                scrollbar = ttk.Scrollbar(products_frame, orient="vertical", command=tree.yview)
                tree.configure(yscrollcommand=scrollbar.set)

                tree.pack(side="left", fill="both", expand=True)
                scrollbar.pack(side="right", fill="y")

                # إضافة المنتجات
                total_amount = 0
                for row in rows:
                    tree.insert("", "end", values=(row[2], f"{row[3]:.2f}", row[4], f"{row[5]:.2f}"))
                    total_amount += row[5]

                # الإجمالي
                tk.Label(details_window, text=f"الإجمالي النهائي: {total_amount:.2f}",
                        font=FONTS['heading'], bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)

                # زر الطباعة
                print_btn = tk.Button(details_window, text="🖨️ طباعة الفاتورة",
                                     command=lambda: print_single_invoice(invoice_id),
                                     bg=COLORS['btn_success'], fg=COLORS['text_white'], font=FONTS['button'])
                print_btn.pack(pady=10)

            cursor.close()
            db.close()

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تحميل تفاصيل الفاتورة: {str(e)}")

    # زر الإغلاق
    close_btn = tk.Button(details_window, text="إغلاق", command=details_window.destroy,
                         bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    close_btn.pack(pady=10)

def print_single_invoice(invoice_id):
    """طباعة فاتورة واحدة"""
    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            cursor.execute("SELECT * FROM sales WHERE invoice_id = %s", (invoice_id,))
            rows = cursor.fetchall()

            if rows:
                first_row = rows[0]

                print("\n" + "="*60)
                print("                    صيدلية الشفاء")
                print("                   فاتورة مبيعات")
                print("="*60)
                print(f"رقم الفاتورة: {invoice_id}")
                print(f"العميل: {first_row[7] or 'عميل'}")
                print(f"الهاتف: {first_row[8] or '-'}")
                print(f"طريقة الدفع: {first_row[6]}")
                print(f"التاريخ: {first_row[9]} - الوقت: {first_row[5]}")
                print("-"*60)
                print(f"{'المنتج':<25} {'السعر':<10} {'الكمية':<8} {'الإجمالي':<12}")
                print("-"*60)

                total_amount = 0
                for row in rows:
                    print(f"{row[2]:<25} {row[3]:<10.2f} {row[4]:<8} {row[5]:<12.2f}")
                    total_amount += row[5]

                print("-"*60)
                print(f"{'الإجمالي النهائي:':<44} {total_amount:<12.2f}")
                print("="*60)
                print("                  شكراً لتعاملكم معنا")
                print("="*60)

                messagebox.showinfo("تم", "تم طباعة الفاتورة في الطرفية")

            cursor.close()
            db.close()

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في طباعة الفاتورة: {str(e)}")

def print_all_invoices_report():
    """طباعة تقرير شامل لجميع الفواتير"""
    try:
        db = connect_db()
        if db:
            cursor = db.cursor()

            # إحصائيات عامة
            cursor.execute("SELECT COUNT(DISTINCT invoice_id), SUM(total) FROM sales")
            total_data = cursor.fetchone()
            total_invoices = total_data[0] or 0
            total_sales = total_data[1] or 0

            # الفواتير
            cursor.execute("""
            SELECT invoice_id, customer, phone, SUM(total) as total_amount,
                   payment_method, date, time
            FROM sales
            GROUP BY invoice_id, customer, phone, payment_method, date, time
            ORDER BY date DESC, time DESC
            """)
            invoices = cursor.fetchall()

            print("\n" + "="*80)
            print("                         صيدلية الشفاء")
            print("                      تقرير شامل للفواتير")
            print("="*80)
            print(f"إجمالي عدد الفواتير: {total_invoices}")
            print(f"إجمالي المبيعات: {total_sales:.2f}")
            print(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("="*80)
            print(f"{'رقم الفاتورة':<20} {'العميل':<15} {'الإجمالي':<10} {'طريقة الدفع':<12} {'التاريخ':<12}")
            print("-"*80)

            for invoice in invoices:
                print(f"{invoice[0]:<20} {(invoice[1] or 'عميل'):<15} {invoice[3]:<10.2f} {invoice[4]:<12} {invoice[5]:<12}")

            print("="*80)
            print(f"{'إجمالي المبيعات:':<60} {total_sales:<15.2f}")
            print("="*80)

            cursor.close()
            db.close()

            messagebox.showinfo("تم", "تم طباعة التقرير الشامل في الطرفية")

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {str(e)}")

def show_users():
    """عرض إدارة المستخدمين الكاملة"""
    clear_content()

    # عنوان الصفحة
    title_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    title_frame.pack(fill="x", pady=10)

    tk.Label(title_frame, text="إدارة المستخدمين", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(side="left", padx=20)

    # زر إضافة مستخدم جديد
    add_user_btn = tk.Button(title_frame, text="+ إضافة مستخدم جديد", command=add_user_window,
                            bg=COLORS['btn_success'], fg=COLORS['text_white'], font=FONTS['button'])
    add_user_btn.pack(side="right", padx=20)

    # إطار البحث
    search_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    search_frame.pack(fill="x", padx=20, pady=10)

    tk.Label(search_frame, text="البحث باسم المستخدم:", font=FONTS['main'], bg=COLORS['bg_main']).pack(side="left")
    global user_search_entry
    user_search_entry = tk.Entry(search_frame, font=FONTS['main'], width=25)
    user_search_entry.pack(side="left", padx=10)

    search_users_btn = tk.Button(search_frame, text="بحث", command=search_users,
                                bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    search_users_btn.pack(side="left", padx=5)

    refresh_users_btn = tk.Button(search_frame, text="تحديث", command=show_users,
                                 bg=COLORS['primary'], fg=COLORS['text_white'], font=FONTS['button'])
    refresh_users_btn.pack(side="left", padx=5)

    # إطار الجدول
    table_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    table_frame.pack(fill="both", expand=True, padx=20, pady=10)

    # إنشاء جدول المستخدمين
    create_users_table(table_frame)

def create_users_table(parent):
    """إنشاء جدول المستخدمين"""
    from tkinter import ttk

    # إنشاء Treeview
    columns = ('اسم المستخدم', 'الصلاحية', 'تاريخ الإنشاء', 'آخر دخول')
    global users_tree
    users_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

    # تعريف العناوين
    for col in columns:
        users_tree.heading(col, text=col)
        users_tree.column(col, width=150, anchor='center')

    # إضافة scrollbar
    scrollbar = ttk.Scrollbar(parent, orient="vertical", command=users_tree.yview)
    users_tree.configure(yscrollcommand=scrollbar.set)

    # تخطيط الجدول
    users_tree.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # تحميل البيانات
    load_users_data()

    # ربط النقر المزدوج لتعديل المستخدم
    users_tree.bind("<Double-1>", lambda event: edit_user())

    return users_tree

def load_users_data():
    """تحميل بيانات المستخدمين"""
    try:
        # مسح البيانات الموجودة
        for item in users_tree.get_children():
            users_tree.delete(item)

        db = connect_db()
        if db:
            cursor = db.cursor()
            cursor.execute("SELECT username, role, created_at, last_login FROM users ORDER BY username")
            rows = cursor.fetchall()

            for row in rows:
                role_name = "مدير النظام" if row[1] == 'admin' else "مستخدم محدود"
                created_at = row[2].strftime('%Y-%m-%d') if row[2] else 'غير محدد'
                last_login = row[3].strftime('%Y-%m-%d %H:%M') if row[3] else 'لم يسجل دخول'

                users_tree.insert("", "end", values=(row[0], role_name, created_at, last_login))

            cursor.close()
            db.close()

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تحميل المستخدمين: {str(e)}")

def search_users():
    """البحث في المستخدمين"""
    search_term = user_search_entry.get().strip()

    if not search_term:
        show_users()
        return

    try:
        # مسح البيانات الموجودة
        for item in users_tree.get_children():
            users_tree.delete(item)

        db = connect_db()
        if db:
            cursor = db.cursor()
            search_query = f"%{search_term}%"
            cursor.execute("SELECT username, role, created_at, last_login FROM users WHERE username LIKE %s ORDER BY username", (search_query,))
            rows = cursor.fetchall()

            for row in rows:
                role_name = "مدير النظام" if row[1] == 'admin' else "مستخدم محدود"
                created_at = row[2].strftime('%Y-%m-%d') if row[2] else 'غير محدد'
                last_login = row[3].strftime('%Y-%m-%d %H:%M') if row[3] else 'لم يسجل دخول'

                users_tree.insert("", "end", values=(row[0], role_name, created_at, last_login))

            cursor.close()
            db.close()

            if not rows:
                messagebox.showinfo("نتيجة البحث", "لم يتم العثور على مستخدمين")

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")

def add_user_window():
    """نافذة إضافة مستخدم جديد"""
    add_window = tk.Toplevel(root)
    add_window.title("إضافة مستخدم جديد")
    add_window.geometry("400x350")
    add_window.configure(bg=COLORS['bg_card'])
    add_window.resizable(False, False)

    # توسيط النافذة
    add_window.transient(root)
    add_window.grab_set()

    # العنوان
    tk.Label(add_window, text="إضافة مستخدم جديد", font=FONTS['title'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)

    # إطار الحقول
    fields_frame = tk.Frame(add_window, bg=COLORS['bg_card'])
    fields_frame.pack(padx=30, pady=20, fill="both", expand=True)

    # اسم المستخدم
    tk.Label(fields_frame, text="اسم المستخدم:", font=FONTS['main'],
            bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))
    username_entry = tk.Entry(fields_frame, font=FONTS['main'], width=30, relief="solid", bd=1)
    username_entry.pack(fill="x", pady=(0, 10))

    # كلمة المرور
    tk.Label(fields_frame, text="كلمة المرور:", font=FONTS['main'],
            bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))
    password_entry = tk.Entry(fields_frame, font=FONTS['main'], width=30, relief="solid", bd=1, show="*")
    password_entry.pack(fill="x", pady=(0, 10))

    # تأكيد كلمة المرور
    tk.Label(fields_frame, text="تأكيد كلمة المرور:", font=FONTS['main'],
            bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))
    confirm_password_entry = tk.Entry(fields_frame, font=FONTS['main'], width=30, relief="solid", bd=1, show="*")
    confirm_password_entry.pack(fill="x", pady=(0, 10))

    # الصلاحية
    tk.Label(fields_frame, text="الصلاحية:", font=FONTS['main'],
            bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))

    role_var = tk.StringVar(value="user")
    role_frame = tk.Frame(fields_frame, bg=COLORS['bg_card'])
    role_frame.pack(fill="x", pady=(0, 10))

    tk.Radiobutton(role_frame, text="مستخدم محدود", variable=role_var, value="user",
                  bg=COLORS['bg_card'], font=FONTS['main']).pack(anchor="w")
    tk.Radiobutton(role_frame, text="مدير النظام", variable=role_var, value="admin",
                  bg=COLORS['bg_card'], font=FONTS['main']).pack(anchor="w")

    # إطار الأزرار
    buttons_frame = tk.Frame(add_window, bg=COLORS['bg_card'])
    buttons_frame.pack(pady=20)

    # زر الحفظ
    save_btn = tk.Button(buttons_frame, text="إضافة المستخدم",
                        command=lambda: save_new_user(username_entry, password_entry, confirm_password_entry, role_var, add_window),
                        bg=COLORS['btn_success'], fg=COLORS['text_white'],
                        font=FONTS['button'], width=15)
    save_btn.pack(side="left", padx=10)

    # زر الإلغاء
    cancel_btn = tk.Button(buttons_frame, text="إلغاء", command=add_window.destroy,
                          bg=COLORS['error'], fg=COLORS['text_white'],
                          font=FONTS['button'], width=15)
    cancel_btn.pack(side="left", padx=10)

def save_new_user(username_entry, password_entry, confirm_password_entry, role_var, window):
    """حفظ مستخدم جديد"""
    try:
        # الحصول على البيانات
        username = username_entry.get().strip()
        password = password_entry.get().strip()
        confirm_password = confirm_password_entry.get().strip()
        role = role_var.get()

        # التحقق من البيانات
        if not all([username, password, confirm_password]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
            return

        if len(username) < 3:
            messagebox.showerror("خطأ", "اسم المستخدم يجب أن يكون 3 أحرف على الأقل")
            return

        if len(password) < 6:
            messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return

        if password != confirm_password:
            messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
            return

        # حفظ في قاعدة البيانات
        db = connect_db()
        if db:
            cursor = db.cursor()

            # التحقق من عدم وجود مستخدم بنفس الاسم
            cursor.execute("SELECT id FROM users WHERE username = %s", (username,))
            if cursor.fetchone():
                messagebox.showerror("خطأ", "يوجد مستخدم بنفس الاسم مسبقاً")
                cursor.close()
                db.close()
                return

            # إدراج المستخدم الجديد
            from datetime import datetime
            now = datetime.now()
            cursor.execute("INSERT INTO users (username, password, role, created_at) VALUES (%s, %s, %s, %s)",
                          (username, password, role, now))

            db.commit()
            cursor.close()
            db.close()

            messagebox.showinfo("نجح", "تم إضافة المستخدم بنجاح!")
            window.destroy()
            show_users()  # تحديث قائمة المستخدمين

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في إضافة المستخدم: {str(e)}")

def edit_user():
    """تعديل مستخدم محدد"""
    selected = users_tree.selection()
    if not selected:
        messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتعديل")
        return

    # الحصول على بيانات المستخدم المحدد
    item = users_tree.item(selected[0])
    values = item['values']
    username = values[0]

    # التحقق من عدم تعديل المستخدم الحالي لنفسه
    if username == current_user['username']:
        messagebox.showwarning("تحذير", "لا يمكنك تعديل بياناتك الخاصة من هنا")
        return

    # إنشاء نافذة التعديل
    edit_window = tk.Toplevel(root)
    edit_window.title(f"تعديل المستخدم - {username}")
    edit_window.geometry("400x400")
    edit_window.configure(bg=COLORS['bg_card'])
    edit_window.resizable(False, False)

    edit_window.transient(root)
    edit_window.grab_set()

    # العنوان
    tk.Label(edit_window, text=f"تعديل المستخدم: {username}", font=FONTS['title'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)

    # إطار الحقول
    fields_frame = tk.Frame(edit_window, bg=COLORS['bg_card'])
    fields_frame.pack(padx=30, pady=20, fill="both", expand=True)

    # كلمة المرور الجديدة
    tk.Label(fields_frame, text="كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية):",
             font=FONTS['main'], bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))
    new_password_entry = tk.Entry(fields_frame, font=FONTS['main'], width=30, relief="solid", bd=1, show="*")
    new_password_entry.pack(fill="x", pady=(0, 10))

    # تأكيد كلمة المرور الجديدة
    tk.Label(fields_frame, text="تأكيد كلمة المرور الجديدة:", font=FONTS['main'],
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))
    confirm_new_password_entry = tk.Entry(fields_frame, font=FONTS['main'], width=30, relief="solid", bd=1, show="*")
    confirm_new_password_entry.pack(fill="x", pady=(0, 10))

    # الصلاحية الحالية
    current_role = "admin" if values[1] == "مدير النظام" else "user"

    tk.Label(fields_frame, text="الصلاحية:", font=FONTS['main'],
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))

    role_var = tk.StringVar(value=current_role)
    role_frame = tk.Frame(fields_frame, bg=COLORS['bg_card'])
    role_frame.pack(fill="x", pady=(0, 10))

    tk.Radiobutton(role_frame, text="مستخدم محدود", variable=role_var, value="user",
                   bg=COLORS['bg_card'], font=FONTS['main']).pack(anchor="w")
    tk.Radiobutton(role_frame, text="مدير النظام", variable=role_var, value="admin",
                   bg=COLORS['bg_card'], font=FONTS['main']).pack(anchor="w")

    # إطار الأزرار
    buttons_frame = tk.Frame(edit_window, bg=COLORS['bg_card'])
    buttons_frame.pack(pady=20)

    # زر الحفظ
    save_btn = tk.Button(buttons_frame, text="حفظ التغييرات",
                        command=lambda: update_user(username, new_password_entry, confirm_new_password_entry, role_var, edit_window),
                        bg=COLORS['btn_success'], fg=COLORS['text_white'],
                        font=FONTS['button'], width=15)
    save_btn.pack(side="left", padx=10)

    # زر الحذف
    delete_btn = tk.Button(buttons_frame, text="حذف المستخدم",
                          command=lambda: delete_user(username, edit_window),
                          bg=COLORS['error'], fg=COLORS['text_white'],
                          font=FONTS['button'], width=15)
    delete_btn.pack(side="left", padx=10)

    # زر الإلغاء
    cancel_btn = tk.Button(buttons_frame, text="إلغاء", command=edit_window.destroy,
                          bg=COLORS['accent'], fg=COLORS['text_white'],
                          font=FONTS['button'], width=15)
    cancel_btn.pack(side="left", padx=10)

def update_user(username, new_password_entry, confirm_new_password_entry, role_var, window):
    """تحديث بيانات المستخدم"""
    try:
        new_password = new_password_entry.get().strip()
        confirm_new_password = confirm_new_password_entry.get().strip()
        role = role_var.get()

        # التحقق من كلمة المرور إذا تم إدخالها
        if new_password:
            if len(new_password) < 6:
                messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                return

            if new_password != confirm_new_password:
                messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
                return

        # تحديث في قاعدة البيانات
        db = connect_db()
        if db:
            cursor = db.cursor()

            if new_password:
                # تحديث كلمة المرور والصلاحية
                cursor.execute("UPDATE users SET password = %s, role = %s WHERE username = %s",
                              (new_password, role, username))
            else:
                # تحديث الصلاحية فقط
                cursor.execute("UPDATE users SET role = %s WHERE username = %s",
                              (role, username))

            db.commit()
            cursor.close()
            db.close()

            messagebox.showinfo("نجح", "تم تحديث المستخدم بنجاح!")
            window.destroy()
            show_users()  # تحديث قائمة المستخدمين

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تحديث المستخدم: {str(e)}")

def delete_user(username, window):
    """حذف مستخدم"""
    # التحقق من عدم حذف المستخدم الحالي
    if username == current_user['username']:
        messagebox.showerror("خطأ", "لا يمكنك حذف حسابك الخاص")
        return

    result = messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف المستخدم '{username}' نهائياً؟")
    if result:
        try:
            db = connect_db()
            if db:
                cursor = db.cursor()
                cursor.execute("DELETE FROM users WHERE username = %s", (username,))
                db.commit()
                cursor.close()
                db.close()

                messagebox.showinfo("نجح", "تم حذف المستخدم بنجاح!")
                window.destroy()
                show_users()  # تحديث قائمة المستخدمين

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف المستخدم: {str(e)}")

def test_database():
    """اختبار قاعدة البيانات"""
    clear_content()
    tk.Label(main_content, text="اختبار قاعدة البيانات", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=30)
    
    result_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    result_frame.pack(pady=20)
    
    db = connect_db()
    if db:
        try:
            cursor = db.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            users_count = cursor.fetchone()[0]
            cursor.close()
            db.close()
            
            tk.Label(result_frame, text="الاتصال بقاعدة البيانات ناجح",
                    font=FONTS['heading'], bg=COLORS['bg_main'], fg=COLORS['btn_success']).pack(pady=10)
            tk.Label(result_frame, text=f"عدد المستخدمين في قاعدة البيانات: {users_count}",
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
        except Exception as e:
            tk.Label(result_frame, text=f"خطأ في قاعدة البيانات: {str(e)}",
                    font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['error']).pack(pady=10)
    else:
        tk.Label(result_frame, text="فشل في الاتصال بقاعدة البيانات",
                font=FONTS['heading'], bg=COLORS['bg_main'], fg=COLORS['error']).pack(pady=10)
        tk.Label(result_frame, text="تأكد من تشغيل MySQL Server",
                font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['error']).pack(pady=5)

def logout():
    """تسجيل الخروج"""
    result = messagebox.askyesno("تسجيل خروج", "هل تريد تسجيل الخروج من النظام؟")
    if result:
        # إعادة تشغيل النظام
        root.destroy()
        main()

def main():
    """الدالة الرئيسية"""
    global root, login_frame, username_entry, password_entry
    
    # إنشاء النافذة الرئيسية
    root = tk.Tk()
    root.title("صيدلية الشفاء - نظام إدارة شامل")
    root.geometry("1200x700")
    root.configure(bg=COLORS['bg_main'])
    
    # توسيط النافذة
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    # شاشة تسجيل الدخول
    login_frame = tk.Frame(root, bg=COLORS['bg_main'])
    login_frame.pack(fill="both", expand=True)
    
    # إطار تسجيل الدخول
    login_container = tk.Frame(login_frame, bg=COLORS['bg_card'], relief="raised", bd=2)
    login_container.place(relx=0.5, rely=0.5, anchor="center", width=400, height=350)
    
    # العنوان
    tk.Label(login_container, text="صيدلية الشفاء", font=FONTS['title'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)
    
    tk.Label(login_container, text="نظام إدارة شامل ومتطور", font=FONTS['main'],
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(pady=5)
    
    tk.Label(login_container, text="تسجيل الدخول", font=FONTS['heading'],
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(pady=15)
    
    # حقول الإدخال
    tk.Label(login_container, text="اسم المستخدم:", font=FONTS['main'],
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", padx=50)
    
    username_entry = tk.Entry(login_container, font=FONTS['main'], width=25, relief="solid", bd=1)
    username_entry.pack(pady=5)
    
    tk.Label(login_container, text="كلمة المرور:", font=FONTS['main'],
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", padx=50)
    
    password_entry = tk.Entry(login_container, font=FONTS['main'], width=25, relief="solid", bd=1, show="*")
    password_entry.pack(pady=5)
    
    # زر تسجيل الدخول
    login_btn = tk.Button(login_container, text="دخول", command=login,
                         bg=COLORS['btn_success'], fg=COLORS['text_white'],
                         font=FONTS['button'], width=20, height=2, relief="flat")
    login_btn.pack(pady=20)
    
    # معلومات تسجيل الدخول
    info_text = "بيانات تسجيل الدخول:\nالمدير: admin / admin123\nالمستخدم: user / user123"
    tk.Label(login_container, text=info_text, font=('Arial', 9),
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack()
    
    # ربط Enter
    username_entry.bind("<Return>", lambda e: password_entry.focus())
    password_entry.bind("<Return>", lambda e: login())
    
    # تركيز على حقل اسم المستخدم
    username_entry.focus()
    
    print("تم تشغيل نظام صيدلية الشفاء")
    print("بيانات تسجيل الدخول:")
    print("   المدير: admin / admin123")
    print("   المستخدم: user / user123")
    
    root.mainloop()

if __name__ == "__main__":
    main()
