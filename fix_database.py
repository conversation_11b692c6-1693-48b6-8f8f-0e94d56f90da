# -*- coding: utf-8 -*-
"""
إصلاح قاعدة البيانات وإضافة الأعمدة المفقودة
Fix Database and Add Missing Columns
"""

import mysql.connector
import sys

def connect_db():
    """الاتصال بقاعدة البيانات"""
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="pharmacy_db"
        )
    except mysql.connector.Error as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def check_and_add_created_at_column():
    """التحقق من وجود عمود created_at وإضافته إذا لم يكن موجوداً"""
    db = connect_db()
    if not db:
        return False

    cursor = db.cursor()

    try:
        # التحقق من وجود عمود created_at في جدول users
        cursor.execute("SHOW COLUMNS FROM users LIKE 'created_at'")
        result = cursor.fetchone()

        if not result:
            print("🔧 إضافة عمود created_at إلى جدول users...")
            cursor.execute("""
                ALTER TABLE users 
                ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            """)
            db.commit()
            print("✅ تم إضافة عمود created_at بنجاح")
        else:
            print("ℹ️  عمود created_at موجود بالفعل")

        return True

    except mysql.connector.Error as e:
        print(f"❌ خطأ في إضافة عمود created_at: {e}")
        return False
    finally:
        cursor.close()
        db.close()

def create_limited_user_if_not_exists():
    """إنشاء المستخدم المحدود إذا لم يكن موجوداً"""
    db = connect_db()
    if not db:
        return False

    cursor = db.cursor()

    try:
        # التحقق من وجود المستخدم المحدود
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'user'")
        count = cursor.fetchone()[0]

        if count == 0:
            print("👤 إنشاء المستخدم المحدود...")
            cursor.execute("""
                INSERT INTO users (username, password, role)
                VALUES ('user', 'user123', 'user')
            """)
            db.commit()
            print("✅ تم إنشاء المستخدم المحدود بنجاح")
            print("   اسم المستخدم: user")
            print("   كلمة المرور: user123")
        else:
            print("ℹ️  المستخدم المحدود موجود بالفعل")

        return True

    except mysql.connector.Error as e:
        print(f"❌ خطأ في إنشاء المستخدم المحدود: {e}")
        return False
    finally:
        cursor.close()
        db.close()

def verify_database_structure():
    """التحقق من بنية قاعدة البيانات"""
    db = connect_db()
    if not db:
        return False

    cursor = db.cursor()

    try:
        print("\n📋 التحقق من بنية قاعدة البيانات:")
        print("-" * 50)

        # التحقق من جدول users
        cursor.execute("DESCRIBE users")
        users_columns = cursor.fetchall()
        
        print("🗂️  جدول users:")
        for column in users_columns:
            column_name = column[0]
            column_type = column[1]
            is_null = "NULL" if column[2] == "YES" else "NOT NULL"
            default_value = f"DEFAULT {column[4]}" if column[4] else ""
            print(f"   {column_name:<15} {column_type:<20} {is_null:<10} {default_value}")

        # عد المستخدمين
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"\n👥 عدد المستخدمين: {user_count}")

        # عرض المستخدمين
        cursor.execute("SELECT username, role FROM users")
        users = cursor.fetchall()
        
        print("\n📝 قائمة المستخدمين:")
        for username, role in users:
            role_name = "مدير النظام" if role == "admin" else "مستخدم محدود"
            print(f"   👤 {username:<10} | {role_name}")

        return True

    except mysql.connector.Error as e:
        print(f"❌ خطأ في التحقق من قاعدة البيانات: {e}")
        return False
    finally:
        cursor.close()
        db.close()

def test_user_management_functions():
    """اختبار وظائف إدارة المستخدمين"""
    print("\n🧪 اختبار وظائف إدارة المستخدمين:")
    print("-" * 50)

    try:
        from user_management import USER_ROLES, get_user_permissions, check_user_permission
        
        # اختبار الأدوار
        print("📋 الأدوار المتاحة:")
        for role_key, role_info in USER_ROLES.items():
            print(f"   {role_info['name_ar']} ({role_key})")
            print(f"      الوصف: {role_info['description']}")
            permissions = get_user_permissions(role_key)
            print(f"      الصلاحيات: {', '.join(permissions)}")
            print()

        # اختبار الصلاحيات
        print("🔐 اختبار الصلاحيات:")
        test_permissions = ['dashboard', 'inventory', 'sales', 'invoice', 'users']
        
        for permission in test_permissions:
            admin_access = check_user_permission('admin', permission)
            user_access = check_user_permission('user', permission)
            
            admin_status = "✅" if admin_access else "❌"
            user_status = "✅" if user_access else "❌"
            
            print(f"   {permission:<12} | المدير: {admin_status} | المستخدم: {user_status}")

        print("\n✅ جميع الاختبارات نجحت!")
        return True

    except ImportError as e:
        print(f"❌ خطأ في استيراد وحدة إدارة المستخدمين: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار الوظائف: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح قاعدة البيانات - صيدلية الشفاء")
    print("=" * 60)

    # إصلاح قاعدة البيانات
    print("\n1️⃣ إصلاح بنية قاعدة البيانات...")
    if not check_and_add_created_at_column():
        print("❌ فشل في إصلاح قاعدة البيانات")
        sys.exit(1)

    # إنشاء المستخدم المحدود
    print("\n2️⃣ إنشاء المستخدم المحدود...")
    if not create_limited_user_if_not_exists():
        print("❌ فشل في إنشاء المستخدم المحدود")
        sys.exit(1)

    # التحقق من بنية قاعدة البيانات
    print("\n3️⃣ التحقق من بنية قاعدة البيانات...")
    if not verify_database_structure():
        print("❌ فشل في التحقق من قاعدة البيانات")
        sys.exit(1)

    # اختبار وظائف إدارة المستخدمين
    print("\n4️⃣ اختبار وظائف إدارة المستخدمين...")
    test_user_management_functions()

    print("\n" + "=" * 60)
    print("✅ تم إصلاح قاعدة البيانات بنجاح!")
    print("\n🚀 يمكنك الآن تشغيل النظام:")
    print("   python main.py")
    print("\n📝 بيانات تسجيل الدخول:")
    print("   👑 المدير:")
    print("      اسم المستخدم: admin")
    print("      كلمة المرور: admin123")
    print("   👤 المستخدم المحدود:")
    print("      اسم المستخدم: user")
    print("      كلمة المرور: user123")
    print("\n💡 ملاحظة:")
    print("   - تم إصلاح خطأ عمود created_at")
    print("   - تم إنشاء المستخدم المحدود")
    print("   - النظام جاهز للاستخدام")

if __name__ == "__main__":
    main()
