# -*- coding: utf-8 -*-
"""
ملف طرق الدفع للمحافظ الإلكترونية الموريتانية
Mauritanian Electronic Payment Methods Configuration
"""

# ==== طرق الدفع المتاحة ====
PAYMENT_METHODS = {
    # النقدي
    'cash': {
        'name_ar': 'نقدي',
        'name_en': 'Cash',
        'icon': '💰',
        'display': '💰 نقدي',
        'type': 'cash',
        'description': 'الدفع النقدي المباشر'
    },
    
    # المحافظ الإلكترونية الموريتانية
    'bankily': {
        'name_ar': 'بنكيلي',
        'name_en': 'Bankily',
        'icon': '📱',
        'display': '📱 بنكيلي (Bankily)',
        'type': 'mobile_wallet',
        'description': 'محفظة بنكيلي الإلكترونية',
        'provider': 'Société Générale Mauritanie',
        'code': 'BANKILY'
    },
    
    'masrafi': {
        'name_ar': 'مصرفي',
        'name_en': 'Masrafi',
        'icon': '🏦',
        'display': '🏦 مصرفي (Masrafi)',
        'type': 'mobile_wallet',
        'description': 'محفظة مصرفي الإلكترونية',
        'provider': 'Banque Nationale de Mauritanie',
        'code': 'MASRAFI'
    },
    
    'sedad': {
        'name_ar': 'السداد',
        'name_en': 'Sedad',
        'icon': '💳',
        'display': '💳 السداد (Sedad)',
        'type': 'mobile_wallet',
        'description': 'محفظة السداد الإلكترونية',
        'provider': 'Banque Mauritanienne pour le Commerce International',
        'code': 'SEDAD'
    },
    
    'bim_bank': {
        'name_ar': 'بيك بنك',
        'name_en': 'BIM Bank',
        'icon': '🏛️',
        'display': '🏛️ بيك بنك (BIM Bank)',
        'type': 'mobile_wallet',
        'description': 'محفظة بيك بنك الإلكترونية',
        'provider': 'Banque Internationale pour la Mauritanie',
        'code': 'BIM'
    },
    
    'click': {
        'name_ar': 'كليك',
        'name_en': 'Click',
        'icon': '🖱️',
        'display': '🖱️ كليك (Click)',
        'type': 'mobile_wallet',
        'description': 'محفظة كليك الإلكترونية',
        'provider': 'Attijari Bank Mauritanie',
        'code': 'CLICK'
    }
}

# ==== قائمة طرق الدفع للعرض في الواجهة ====
PAYMENT_METHODS_LIST = [
    PAYMENT_METHODS['cash']['display'],
    PAYMENT_METHODS['bankily']['display'],
    PAYMENT_METHODS['masrafi']['display'],
    PAYMENT_METHODS['sedad']['display'],
    PAYMENT_METHODS['bim_bank']['display'],
    PAYMENT_METHODS['click']['display']
]

# ==== خريطة تنظيف طرق الدفع ====
PAYMENT_CLEANUP_MAP = {
    '💰 نقدي': 'نقدي',
    '📱 بنكيلي (Bankily)': 'بنكيلي',
    '🏦 مصرفي (Masrafi)': 'مصرفي',
    '💳 السداد (Sedad)': 'السداد',
    '🏛️ بيك بنك (BIM Bank)': 'بيك بنك',
    '🖱️ كليك (Click)': 'كليك'
}

# ==== خريطة عكسية لإعادة التنسيق ====
PAYMENT_REVERSE_MAP = {
    'نقدي': '💰 نقدي',
    'بنكيلي': '📱 بنكيلي (Bankily)',
    'مصرفي': '🏦 مصرفي (Masrafi)',
    'السداد': '💳 السداد (Sedad)',
    'بيك بنك': '🏛️ بيك بنك (BIM Bank)',
    'كليك': '🖱️ كليك (Click)'
}

# ==== معلومات إضافية عن المحافظ ====
WALLET_INFO = {
    'bankily': {
        'phone_prefix': '+222',
        'min_amount': 100,  # أوقية
        'max_amount': 500000,  # أوقية
        'fees': 'حسب تعرفة البنك',
        'availability': '24/7'
    },
    
    'masrafi': {
        'phone_prefix': '+222',
        'min_amount': 50,
        'max_amount': 300000,
        'fees': 'حسب تعرفة البنك',
        'availability': '24/7'
    },
    
    'sedad': {
        'phone_prefix': '+222',
        'min_amount': 100,
        'max_amount': 400000,
        'fees': 'حسب تعرفة البنك',
        'availability': '24/7'
    },
    
    'bim_bank': {
        'phone_prefix': '+222',
        'min_amount': 100,
        'max_amount': 600000,
        'fees': 'حسب تعرفة البنك',
        'availability': '24/7'
    },
    
    'click': {
        'phone_prefix': '+222',
        'min_amount': 50,
        'max_amount': 250000,
        'fees': 'حسب تعرفة البنك',
        'availability': '24/7'
    }
}

# ==== دوال مساعدة ====
def get_payment_methods_list():
    """الحصول على قائمة طرق الدفع للعرض في الواجهة"""
    return PAYMENT_METHODS_LIST

def clean_payment_method(method_display):
    """تنظيف طريقة الدفع من الأيقونات للحفظ في قاعدة البيانات"""
    return PAYMENT_CLEANUP_MAP.get(method_display, method_display)

def format_payment_method(method_clean):
    """تنسيق طريقة الدفع للعرض في الواجهة"""
    return PAYMENT_REVERSE_MAP.get(method_clean, method_clean)

def get_payment_method_info(method_key):
    """الحصول على معلومات طريقة دفع محددة"""
    return PAYMENT_METHODS.get(method_key, {})

def get_wallet_info(method_key):
    """الحصول على معلومات المحفظة الإلكترونية"""
    return WALLET_INFO.get(method_key, {})

def is_mobile_wallet(method_display):
    """التحقق من كون طريقة الدفع محفظة إلكترونية"""
    clean_method = clean_payment_method(method_display)
    for key, info in PAYMENT_METHODS.items():
        if info['name_ar'] == clean_method:
            return info['type'] == 'mobile_wallet'
    return False

def get_payment_icon(method_display):
    """الحصول على أيقونة طريقة الدفع"""
    clean_method = clean_payment_method(method_display)
    for key, info in PAYMENT_METHODS.items():
        if info['name_ar'] == clean_method:
            return info['icon']
    return '💳'

def validate_payment_amount(method_display, amount):
    """التحقق من صحة المبلغ لطريقة الدفع المحددة"""
    if method_display == '💰 نقدي':
        return True, ""
    
    clean_method = clean_payment_method(method_display)
    
    # البحث عن معلومات المحفظة
    wallet_key = None
    for key, info in PAYMENT_METHODS.items():
        if info['name_ar'] == clean_method:
            wallet_key = key
            break
    
    if not wallet_key or wallet_key not in WALLET_INFO:
        return True, ""
    
    wallet_info = WALLET_INFO[wallet_key]
    min_amount = wallet_info.get('min_amount', 0)
    max_amount = wallet_info.get('max_amount', float('inf'))
    
    if amount < min_amount:
        return False, f"الحد الأدنى للدفع عبر {clean_method}: {min_amount} أوقية"
    
    if amount > max_amount:
        return False, f"الحد الأقصى للدفع عبر {clean_method}: {max_amount} أوقية"
    
    return True, ""

def get_payment_methods_summary():
    """الحصول على ملخص جميع طرق الدفع"""
    summary = []
    for key, info in PAYMENT_METHODS.items():
        summary.append({
            'display': info['display'],
            'type': info['type'],
            'description': info['description'],
            'provider': info.get('provider', 'N/A')
        })
    return summary

# ==== إعدادات العملة ====
CURRENCY_SETTINGS = {
    'primary': {
        'code': 'MRU',
        'name_ar': 'أوقية موريتانية',
        'name_en': 'Mauritanian Ouguiya',
        'symbol': 'UM',
        'decimal_places': 2
    },
    'secondary': {
        'code': 'GHS',
        'name_ar': 'سيدي غاني',
        'name_en': 'Ghanaian Cedi',
        'symbol': 'Ghc',
        'decimal_places': 2
    }
}

def format_currency(amount, currency='secondary'):
    """تنسيق المبلغ بالعملة المحددة"""
    currency_info = CURRENCY_SETTINGS.get(currency, CURRENCY_SETTINGS['secondary'])
    symbol = currency_info['symbol']
    decimal_places = currency_info['decimal_places']
    
    return f"{symbol} {amount:.{decimal_places}f}"

# ==== تصدير المتغيرات الرئيسية ====
__all__ = [
    'PAYMENT_METHODS',
    'PAYMENT_METHODS_LIST',
    'PAYMENT_CLEANUP_MAP',
    'PAYMENT_REVERSE_MAP',
    'WALLET_INFO',
    'get_payment_methods_list',
    'clean_payment_method',
    'format_payment_method',
    'get_payment_method_info',
    'get_wallet_info',
    'is_mobile_wallet',
    'get_payment_icon',
    'validate_payment_amount',
    'get_payment_methods_summary',
    'CURRENCY_SETTINGS',
    'format_currency'
]
