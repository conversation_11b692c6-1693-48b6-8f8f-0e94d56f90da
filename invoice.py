# -*- coding: utf-8 -*-
import tkinter as tk
from tkinter import ttk, messagebox
import mysql.connector
from datetime import datetime
import os
import subprocess
import platform
import sys
from interface_manager import register_invoices_interface

# إعداد الترميز للنصوص العربية
if sys.platform.startswith('win'):
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'Arabic_Saudi Arabia.1256')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'ar_SA.UTF-8')
        except:
            pass

def connect_db():
    return mysql.connector.connect(
        host="localhost",
        user="root",
        password="",
        database="pharmacy_db"
    )

def print_selected_invoice():
    """طباعة الفاتورة المحددة"""
    selected = invoice_tree.selection()
    if not selected:
        messagebox.showwarning("تحذير", "يرجى اختيار فاتورة للطباعة")
        return

    # الحصول على رقم الفاتورة
    item = invoice_tree.item(selected[0])
    invoice_id = item['values'][0]

    # جلب تفاصيل الفاتورة من قاعدة البيانات
    try:
        db = connect_db()
        cursor = db.cursor()
        cursor.execute("""SELECT product_name, price, qty, total, customer, phone, payment_method, date, time
                         FROM sales WHERE invoice_id = %s""", (invoice_id,))
        invoice_data = cursor.fetchall()
        cursor.close()
        db.close()

        if invoice_data:
            # إنشاء محتوى الفاتورة
            customer_name = invoice_data[0][4]
            phone = invoice_data[0][5]
            payment_method = invoice_data[0][6]
            invoice_date = datetime.strptime(f"{invoice_data[0][7]} {invoice_data[0][8]}", "%Y-%m-%d %H:%M:%S")

            cart_items = [(row[0], row[1], row[2], row[3]) for row in invoice_data]

            # طباعة الفاتورة
            print_invoice_content(invoice_id, cart_items, customer_name, phone, payment_method, invoice_date)
        else:
            messagebox.showerror("خطأ", "لم يتم العثور على تفاصيل الفاتورة")

    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ أثناء طباعة الفاتورة: {str(e)}")

def print_invoice_content(invoice_id, cart_items, customer_name, phone, payment_method, invoice_date):
    """طباعة محتوى الفاتورة"""
    try:
        # إنشاء محتوى الفاتورة
        total_amount = sum(item[3] for item in cart_items)

        content = f"""
{'='*60}
                    🏥 صيدلية الشفاء
                   Pharmacy Management System
{'='*60}

رقم الفاتورة: {invoice_id}
التاريخ: {invoice_date.strftime('%Y-%m-%d')}
الوقت: {invoice_date.strftime('%H:%M:%S')}

{'='*60}
                    بيانات العميل
{'='*60}

اسم العميل: {customer_name}
رقم الهاتف: {phone if phone else 'غير محدد'}
طريقة الدفع: {payment_method}

{'='*60}
                    تفاصيل المشتريات
{'='*60}

{'المنتج':<25} {'السعر':<10} {'الكمية':<8} {'الإجمالي':<12}
{'-'*60}
"""

        # إضافة تفاصيل المنتجات
        for item in cart_items:
            product_name = item[0][:24]
            price = f"Ghc {item[1]:.2f}"
            quantity = str(item[2])
            item_total = f"Ghc {item[3]:.2f}"

            content += f"{product_name:<25} {price:<10} {quantity:<8} {item_total:<12}\n"

        content += f"""
{'-'*60}
{'الإجمالي النهائي:':<45} Ghc {total_amount:.2f}
{'='*60}

                    شكراً لتعاملكم معنا
                   نتمنى لكم دوام الصحة والعافية

{'='*60}

تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        # حفظ وفتح الفاتورة
        filename = f"reprint_invoice_{invoice_id}.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        # فتح الفاتورة للطباعة
        if platform.system() == "Windows":
            os.startfile(filename)
        elif platform.system() == "Darwin":  # macOS
            subprocess.call(["open", filename])
        else:  # Linux
            subprocess.call(["xdg-open", filename])

        messagebox.showinfo("تم", "تم فتح الفاتورة للطباعة")

    except Exception as e:
        messagebox.showerror("خطأ في الطباعة", f"حدث خطأ أثناء طباعة الفاتورة: {str(e)}")

def invoice_page(root):
    global invoice_tree

    frame = tk.Frame(root, bg="#f8f9fa")

    # إطار العنوان مع تصميم أنيق
    header_frame = tk.Frame(frame, bg="#2c3e50", height=60)
    header_frame.pack(fill="x", pady=(0, 20))
    header_frame.pack_propagate(False)

    tk.Label(header_frame, text="🧾 إدارة الفواتير", font=("Arial", 18, "bold"),
             bg="#2c3e50", fg="white").pack(expand=True)

    # إطار البحث والتصفية
    search_frame = tk.Frame(frame, bg="#ffffff", relief="raised", bd=2)
    search_frame.pack(pady=10, padx=20, fill="x")

    tk.Label(search_frame, text="🔍 البحث والتصفية", font=("Arial", 14, "bold"),
             bg="#ffffff", fg="#2c3e50").grid(row=0, column=0, columnspan=4, pady=10)

    # حقول البحث
    tk.Label(search_frame, text="اسم العميل:", font=("Arial", 11),
             bg="#ffffff", fg="#34495e").grid(row=1, column=0, sticky="e", padx=10, pady=5)
    customer_search = tk.Entry(search_frame, width=20, font=("Arial", 10))
    customer_search.grid(row=1, column=1, padx=10, pady=5)

    tk.Label(search_frame, text="رقم الفاتورة:", font=("Arial", 11),
             bg="#ffffff", fg="#34495e").grid(row=1, column=2, sticky="e", padx=10, pady=5)
    invoice_search = tk.Entry(search_frame, width=20, font=("Arial", 10))
    invoice_search.grid(row=1, column=3, padx=10, pady=5)

    # إطار الجدول
    table_frame = tk.Frame(frame, bg="#ffffff", relief="raised", bd=2)
    table_frame.pack(pady=10, padx=20, fill="both", expand=True)

    tk.Label(table_frame, text="📊 قائمة الفواتير", font=("Arial", 14, "bold"),
             bg="#ffffff", fg="#2c3e50").pack(pady=10)

    # تحسين تصميم الجدول
    style = ttk.Style()
    style.configure("Invoice.Treeview.Heading", font=("Arial", 11, "bold"))
    style.configure("Invoice.Treeview", font=("Arial", 10))

    invoice_tree = ttk.Treeview(table_frame, columns=("invoice_id", "customer", "phone", "total", "method", "date", "time"),
                               show="headings", height=15, style="Invoice.Treeview")

    headers = ["رقم الفاتورة", "العميل", "الهاتف", "الإجمالي", "طريقة الدفع", "التاريخ", "الوقت"]
    for i, col in enumerate(["invoice_id", "customer", "phone", "total", "method", "date", "time"]):
        invoice_tree.heading(col, text=headers[i])
        if col == "invoice_id":
            invoice_tree.column(col, width=150, anchor="center")
        elif col in ["customer", "phone"]:
            invoice_tree.column(col, width=120, anchor="center")
        else:
            invoice_tree.column(col, width=100, anchor="center")

    # إضافة شريط التمرير
    scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=invoice_tree.yview)
    invoice_tree.configure(yscrollcommand=scrollbar.set)

    invoice_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=(0, 10))
    scrollbar.pack(side="right", fill="y", pady=(0, 10))

    def load_invoices(search_customer="", search_invoice=""):
        # مسح البيانات الحالية
        for item in invoice_tree.get_children():
            invoice_tree.delete(item)

        try:
            db = connect_db()
            cursor = db.cursor()

            # بناء استعلام البحث
            query = """SELECT DISTINCT invoice_id, customer, phone,
                      SUM(total) as total_amount, payment_method, date, time
                      FROM sales WHERE 1=1"""
            params = []

            if search_customer:
                query += " AND customer LIKE %s"
                params.append(f"%{search_customer}%")

            if search_invoice:
                query += " AND invoice_id LIKE %s"
                params.append(f"%{search_invoice}%")

            query += " GROUP BY invoice_id, customer, phone, payment_method, date, time ORDER BY date DESC, time DESC"

            cursor.execute(query, params)
            invoices = cursor.fetchall()

            for invoice in invoices:
                invoice_tree.insert("", "end", values=(
                    invoice[0],  # invoice_id
                    invoice[1],  # customer
                    invoice[2] if invoice[2] else "غير محدد",  # phone
                    f"Ghc {invoice[3]:.2f}",  # total
                    invoice[4],  # payment_method
                    invoice[5],  # date
                    invoice[6]   # time
                ))

            cursor.close()
            db.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الفواتير: {str(e)}")

    def search_invoices():
        customer = customer_search.get().strip()
        invoice_id = invoice_search.get().strip()
        load_invoices(customer, invoice_id)

    def clear_search():
        customer_search.delete(0, tk.END)
        invoice_search.delete(0, tk.END)
        load_invoices()

    # أزرار البحث
    buttons_search_frame = tk.Frame(search_frame, bg="#ffffff")
    buttons_search_frame.grid(row=2, column=0, columnspan=4, pady=15)

    tk.Button(buttons_search_frame, text="🔍 بحث", command=search_invoices,
             bg="#3498db", fg="white", font=("Arial", 11, "bold"),
             width=12, height=1, relief="flat", cursor="hand2").pack(side="left", padx=5)

    tk.Button(buttons_search_frame, text="🔄 مسح", command=clear_search,
             bg="#95a5a6", fg="white", font=("Arial", 11, "bold"),
             width=12, height=1, relief="flat", cursor="hand2").pack(side="left", padx=5)

    # إطار الأزرار الرئيسية
    buttons_frame = tk.Frame(frame, bg="#f8f9fa")
    buttons_frame.pack(pady=20)

    tk.Button(buttons_frame, text="🖨️ طباعة الفاتورة", command=print_selected_invoice,
             bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
             width=18, height=2, relief="flat", cursor="hand2").pack(side="left", padx=10)

    tk.Button(buttons_frame, text="🔄 تحديث القائمة", command=lambda: load_invoices(),
             bg="#3498db", fg="white", font=("Arial", 12, "bold"),
             width=18, height=2, relief="flat", cursor="hand2").pack(side="left", padx=10)

    # تحميل الفواتير عند بدء التشغيل
    load_invoices()

    # تسجيل واجهة الفواتير في مدير الواجهات
    register_invoices_interface(frame)

    return frame
