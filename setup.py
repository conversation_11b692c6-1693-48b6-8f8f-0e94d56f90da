#!/usr/bin/env python3
"""
إعداد نظام إدارة الصيدلية
تشغيل هذا الملف لإعداد النظام للمرة الأولى
"""

import os
import sys
import subprocess
import mysql.connector
from update_database import main as setup_database

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 6):
        print("❌ يتطلب النظام Python 3.6 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ إصدار Python مناسب: {sys.version}")
    return True

def check_mysql_connection():
    """التحقق من اتصال MySQL"""
    try:
        connection = mysql.connector.connect(
            host="localhost",
            user="root",
            password=""
        )
        connection.close()
        print("✅ اتصال MySQL متاح")
        return True
    except mysql.connector.Error as e:
        print(f"❌ خطأ في اتصال MySQL: {e}")
        print("تأكد من:")
        print("- تشغيل خادم MySQL")
        print("- صحة بيانات الاتصال (المستخدم: root، كلمة المرور: فارغة)")
        return False

def install_requirements():
    """تثبيت المتطلبات"""
    requirements = [
        "mysql-connector-python",
        "tkinter"  # عادة مثبت مع Python
    ]
    
    print("📦 تثبيت المتطلبات...")
    
    for package in requirements:
        try:
            if package == "tkinter":
                # التحقق من tkinter
                import tkinter
                print(f"✅ {package} متاح")
            else:
                # محاولة استيراد الحزمة
                __import__(package.replace("-", "_"))
                print(f"✅ {package} مثبت مسبقاً")
        except ImportError:
            print(f"📥 تثبيت {package}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ تم تثبيت {package}")
            except subprocess.CalledProcessError:
                print(f"❌ فشل في تثبيت {package}")
                return False
    
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        "invoices",  # مجلد الفواتير
        "backups",   # مجلد النسخ الاحتياطية
        "logs"       # مجلد السجلات
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ تم إنشاء مجلد: {directory}")
        else:
            print(f"✅ مجلد موجود: {directory}")

def setup_database():
    """إعداد قاعدة البيانات"""
    print("🗄️ إعداد قاعدة البيانات...")
    try:
        setup_database()
        return True
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def create_config_file():
    """إنشاء ملف الإعدادات"""
    config_content = """# إعدادات نظام إدارة الصيدلية

# إعدادات قاعدة البيانات
DB_HOST = "localhost"
DB_USER = "root"
DB_PASSWORD = ""
DB_NAME = "pharmacy_db"

# إعدادات النظام
SYSTEM_NAME = "صيدلية الشفاء - نظام إدارة شامل"
PHARMACY_NAME = "صيدلية الشفاء"
PHARMACY_NAME_EN = "Al-Shifa Pharmacy"
VERSION = "2.0"

# إعدادات الطباعة
INVOICE_TEMPLATE = "default"
AUTO_PRINT = True

# إعدادات النسخ الاحتياطي
BACKUP_ENABLED = True
BACKUP_INTERVAL = 24  # ساعات
"""
    
    with open("config.py", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print("✅ تم إنشاء ملف الإعدادات: config.py")

def run_tests():
    """تشغيل اختبارات سريعة"""
    print("🧪 تشغيل اختبارات النظام...")
    
    try:
        # اختبار استيراد الوحدات الرئيسية
        import inventory
        import sales
        import invoice
        import dashboard
        import validation
        import theme
        print("✅ جميع الوحدات متاحة")
        
        # اختبار الاتصال بقاعدة البيانات
        from update_database import connect_db
        db = connect_db()
        if db:
            db.close()
            print("✅ اتصال قاعدة البيانات يعمل")
        else:
            print("❌ مشكلة في اتصال قاعدة البيانات")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبارات: {e}")
        return False

def main():
    """الدالة الرئيسية للإعداد"""
    print("🏥 مرحباً بك في إعداد نظام إدارة الصيدلية")
    print("=" * 50)
    
    # التحقق من إصدار Python
    if not check_python_version():
        sys.exit(1)
    
    # التحقق من MySQL
    if not check_mysql_connection():
        print("\n💡 تعليمات إعداد MySQL:")
        print("1. تثبيت MySQL Server")
        print("2. تشغيل خدمة MySQL")
        print("3. التأكد من إمكانية الاتصال بالمستخدم root")
        sys.exit(1)
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        sys.exit(1)
    
    # إنشاء المجلدات
    create_directories()
    
    # إعداد قاعدة البيانات
    if not setup_database():
        sys.exit(1)
    
    # إنشاء ملف الإعدادات
    create_config_file()
    
    # تشغيل الاختبارات
    if not run_tests():
        print("❌ فشلت بعض الاختبارات")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 تم إعداد النظام بنجاح!")
    print("\n📋 معلومات تسجيل الدخول:")
    print("اسم المستخدم: admin")
    print("كلمة المرور: admin123")
    print("\n🚀 لتشغيل النظام:")
    print("python main.py")
    print("\n📚 للمساعدة:")
    print("راجع ملف USER_GUIDE.md")

if __name__ == "__main__":
    main()
