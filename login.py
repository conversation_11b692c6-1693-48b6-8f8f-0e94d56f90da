import tkinter as tk
from tkinter import messagebox
import mysql.connector

# الاتصال بقاعدة البيانات
def connect_db():
    return mysql.connector.connect(
        host="localhost",
        user="root",
        password="your_password",  # ← غيّرها حسب إعدادك
        database="pharmacy_db"
    )

# التحقق من تسجيل الدخول
def login():
    username = username_entry.get()
    password = password_entry.get()

    if not username or not password:
        messagebox.showerror("خطأ", "يرجى ملء كل الحقول.")
        return

    db = connect_db()
    cursor = db.cursor()
    query = "SELECT * FROM users WHERE username=%s AND password=%s"
    cursor.execute(query, (username, password))
    result = cursor.fetchone()

    if result:
        messagebox.showinfo("تم", f"مرحبًا {result[1]}! تسجيل الدخول ناجح كـ {result[3]}.")
        root.destroy()  # أو الانتقال إلى الصفحة الرئيسية لاحقًا
    else:
        messagebox.showerror("فشل", "اسم المستخدم أو كلمة المرور غير صحيحة.")

    cursor.close()
    db.close()

# تصميم الواجهة
root = tk.Tk()
root.title("تسجيل الدخول - إدارة صيدلية")
root.geometry("400x250")
root.configure(bg="#e8f0f8")

title = tk.Label(root, text="PHARMACY SYSTEM LOGIN", font=("Helvetica", 16, "bold"), bg="#e8f0f8", fg="green")
title.pack(pady=20)

frame = tk.Frame(root, bg="#e8f0f8")
frame.pack(pady=10)

tk.Label(frame, text="Username:", font=("Arial", 12), bg="#e8f0f8").grid(row=0, column=0, sticky="e", pady=5)
username_entry = tk.Entry(frame, width=25)
username_entry.grid(row=0, column=1)

tk.Label(frame, text="Password:", font=("Arial", 12), bg="#e8f0f8").grid(row=1, column=0, sticky="e", pady=5)
password_entry = tk.Entry(frame, show="*", width=25)
password_entry.grid(row=1, column=1)

login_btn = tk.Button(root, text="Login", command=login, bg="green", fg="white", width=15)
login_btn.pack(pady=10)

root.mainloop()
