
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم INV-20250713042259</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .invoice-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
            background: #f8f9fa;
        }

        .info-section {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-right: 4px solid #27ae60;
        }

        .info-section h3 {
            color: #2c3e50;
            font-size: 1.3em;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #34495e;
        }

        .info-value {
            color: #2c3e50;
        }

        .products-section {
            padding: 30px;
        }

        .products-title {
            text-align: center;
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .products-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .products-table th {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 15px;
            text-align: center;
            font-weight: 600;
            font-size: 1.1em;
        }

        .products-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
            transition: background-color 0.3s ease;
        }

        .products-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .products-table tr:hover {
            background-color: #e8f5e8;
        }

        .total-row {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%) !important;
            color: white !important;
            font-weight: 700;
            font-size: 1.2em;
        }

        .total-row td {
            border-bottom: none !important;
        }

        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .footer h3 {
            font-size: 1.5em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .footer p {
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .print-date {
            font-size: 0.9em;
            opacity: 0.7;
            margin-top: 20px;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }

            .invoice-container {
                box-shadow: none;
                border-radius: 0;
            }
        }

        .print-button {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 10px 25px rgba(39, 174, 96, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .print-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(39, 174, 96, 0.4);
        }

        @media print {
            .print-button {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="header">
            <h1>🏥 صيدلية الشفاء</h1>
            <p>Al-Shifa Pharmacy - نظام إدارة شامل ومتطور</p>
        </div>

        <div class="invoice-info">
            <div class="info-section">
                <h3>📋 معلومات الفاتورة</h3>
                <div class="info-item">
                    <span class="info-label">رقم الفاتورة:</span>
                    <span class="info-value">INV-20250713042259</span>
                </div>
                <div class="info-item">
                    <span class="info-label">التاريخ:</span>
                    <span class="info-value">2025-07-13</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الوقت:</span>
                    <span class="info-value">04:22:59</span>
                </div>
            </div>

            <div class="info-section">
                <h3>👤 بيانات العميل</h3>
                <div class="info-item">
                    <span class="info-label">اسم العميل:</span>
                    <span class="info-value">tttttt</span>
                </div>
                <div class="info-item">
                    <span class="info-label">رقم الهاتف:</span>
                    <span class="info-value">444444444</span>
                </div>
                <div class="info-item">
                    <span class="info-label">طريقة الدفع:</span>
                    <span class="info-value">نقدي</span>
                </div>
            </div>
        </div>

        <div class="products-section">
            <h2 class="products-title">🛒 تفاصيل المشتريات</h2>
            <table class="products-table">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>السعر</th>
                        <th>الكمية</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>lait</td>
                        <td>Ghc 400.00</td>
                        <td>1</td>
                        <td>Ghc 400.00</td>
                    </tr>
                    <tr class="total-row">
                        <td colspan="3">الإجمالي النهائي</td>
                        <td>Ghc 400.00</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="footer">
            <h3>🙏 شكراً لتعاملكم معنا</h3>
            <p>نتمنى لكم دوام الصحة والعافية</p>
            <div class="print-date">
                تاريخ الطباعة: 2025-07-13 04:22:59
            </div>
        </div>
    </div>

    <button class="print-button" onclick="window.print()">🖨️ طباعة</button>

    <script>
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للجدول
            const rows = document.querySelectorAll('.products-table tr:not(.total-row)');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.transition = 'all 0.3s ease';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // طباعة تلقائية بعد التحميل (اختياري)
            // setTimeout(() => window.print(), 1000);
        });
    </script>
</body>
</html>