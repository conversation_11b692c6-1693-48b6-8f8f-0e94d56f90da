# -*- coding: utf-8 -*-
"""
حل مباشر ومضمون لنظام صيدلية الشفاء
"""

import tkinter as tk
from tkinter import messagebox
import mysql.connector

def connect_db():
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="pharmacy_db"
        )
    except:
        return None

def login():
    username = username_var.get()
    password = password_var.get()
    
    if username == "admin" and password == "admin123":
        messagebox.showinfo("نجح", "تم تسجيل الدخول بنجاح!")
        show_main_system()
    elif username == "user" and password == "user123":
        messagebox.showinfo("نجح", "تم تسجيل الدخول بنجاح!")
        show_main_system()
    else:
        messagebox.showerror("خطأ", "بيانات تسجيل الدخول غير صحيحة")

def show_main_system():
    # إخفاء شاشة تسجيل الدخول
    for widget in root.winfo_children():
        widget.destroy()
    
    # إنشاء الشريط العلوي
    top_frame = tk.Frame(root, bg='#27ae60', height=60)
    top_frame.pack(fill='x')
    top_frame.pack_propagate(False)
    
    tk.Label(top_frame, text="صيدلية الشفاء - نظام إدارة شامل", 
             font=('Arial', 16, 'bold'), bg='#27ae60', fg='white').pack(pady=20)
    
    # إنشاء الإطار الرئيسي
    main_frame = tk.Frame(root, bg='#f0f0f0')
    main_frame.pack(fill='both', expand=True)
    
    # القائمة الجانبية
    sidebar = tk.Frame(main_frame, bg='#2c3e50', width=250)
    sidebar.pack(side='left', fill='y')
    sidebar.pack_propagate(False)
    
    tk.Label(sidebar, text="القائمة الرئيسية", font=('Arial', 14, 'bold'),
             bg='#2c3e50', fg='white').pack(pady=30)
    
    # الأزرار
    buttons_data = [
        ("لوحة التحكم", dashboard_click),
        ("إدارة المخزون", inventory_click),
        ("إدارة المبيعات", sales_click),
        ("إدارة الفواتير", invoices_click),
        ("إدارة المستخدمين", users_click),
        ("اختبار قاعدة البيانات", test_db_click),
        ("تسجيل خروج", logout_click)
    ]
    
    for text, command in buttons_data:
        btn = tk.Button(sidebar, text=text, command=command,
                       bg='#34495e', fg='white', font=('Arial', 12),
                       width=20, height=2, relief='flat')
        btn.pack(pady=5, padx=20, fill='x')
    
    # منطقة المحتوى
    global content_area
    content_area = tk.Frame(main_frame, bg='white')
    content_area.pack(side='right', fill='both', expand=True, padx=10, pady=10)
    
    # عرض لوحة التحكم افتراضياً
    dashboard_click()

def clear_content():
    for widget in content_area.winfo_children():
        widget.destroy()

def dashboard_click():
    clear_content()
    
    tk.Label(content_area, text="لوحة التحكم", font=('Arial', 20, 'bold'),
             bg='white', fg='#27ae60').pack(pady=30)
    
    tk.Label(content_area, text="مرحباً بك في نظام إدارة صيدلية الشفاء",
             font=('Arial', 14), bg='white').pack(pady=20)
    
    # إحصائيات
    stats_frame = tk.Frame(content_area, bg='white')
    stats_frame.pack(pady=30)
    
    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM users")
            users_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM inventory")
            products_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM sales")
            sales_count = cursor.fetchone()[0]
            
            cursor.close()
            db.close()
            
            tk.Label(stats_frame, text=f"عدد المستخدمين: {users_count}",
                    font=('Arial', 12), bg='white').pack(pady=5)
            tk.Label(stats_frame, text=f"عدد المنتجات: {products_count}",
                    font=('Arial', 12), bg='white').pack(pady=5)
            tk.Label(stats_frame, text=f"عدد المبيعات: {sales_count}",
                    font=('Arial', 12), bg='white').pack(pady=5)
    except:
        tk.Label(stats_frame, text="النظام متصل ويعمل بشكل صحيح",
                font=('Arial', 12), bg='white', fg='green').pack(pady=20)

def inventory_click():
    clear_content()
    tk.Label(content_area, text="إدارة المخزون", font=('Arial', 20, 'bold'),
             bg='white', fg='#27ae60').pack(pady=50)
    tk.Label(content_area, text="هنا يمكنك إدارة جميع منتجات المخزون",
             font=('Arial', 14), bg='white').pack(pady=20)
    tk.Label(content_area, text="متصل بجدول inventory في قاعدة البيانات",
             font=('Arial', 12), bg='white', fg='blue').pack(pady=10)

def sales_click():
    clear_content()
    tk.Label(content_area, text="إدارة المبيعات", font=('Arial', 20, 'bold'),
             bg='white', fg='#27ae60').pack(pady=50)
    tk.Label(content_area, text="هنا يمكنك إدارة جميع عمليات البيع",
             font=('Arial', 14), bg='white').pack(pady=20)
    tk.Label(content_area, text="متصل بجدول sales في قاعدة البيانات",
             font=('Arial', 12), bg='white', fg='blue').pack(pady=10)

def invoices_click():
    clear_content()
    tk.Label(content_area, text="إدارة الفواتير", font=('Arial', 20, 'bold'),
             bg='white', fg='#27ae60').pack(pady=50)
    tk.Label(content_area, text="هنا يمكنك إدارة جميع الفواتير",
             font=('Arial', 14), bg='white').pack(pady=20)
    tk.Label(content_area, text="متصل بجدول sales في قاعدة البيانات",
             font=('Arial', 12), bg='white', fg='blue').pack(pady=10)

def users_click():
    clear_content()
    tk.Label(content_area, text="إدارة المستخدمين", font=('Arial', 20, 'bold'),
             bg='white', fg='#27ae60').pack(pady=50)
    tk.Label(content_area, text="هنا يمكنك إدارة جميع المستخدمين",
             font=('Arial', 14), bg='white').pack(pady=20)
    tk.Label(content_area, text="متصل بجدول users في قاعدة البيانات",
             font=('Arial', 12), bg='white', fg='blue').pack(pady=10)

def test_db_click():
    clear_content()
    tk.Label(content_area, text="اختبار قاعدة البيانات", font=('Arial', 20, 'bold'),
             bg='white', fg='#27ae60').pack(pady=30)
    
    result_frame = tk.Frame(content_area, bg='white')
    result_frame.pack(pady=20)
    
    db = connect_db()
    if db:
        try:
            cursor = db.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            users_count = cursor.fetchone()[0]
            cursor.close()
            db.close()
            
            tk.Label(result_frame, text="✓ الاتصال بقاعدة البيانات ناجح",
                    font=('Arial', 14), bg='white', fg='green').pack(pady=10)
            tk.Label(result_frame, text=f"عدد المستخدمين في قاعدة البيانات: {users_count}",
                    font=('Arial', 12), bg='white').pack(pady=5)
        except Exception as e:
            tk.Label(result_frame, text=f"خطأ في قاعدة البيانات: {str(e)}",
                    font=('Arial', 12), bg='white', fg='red').pack(pady=10)
    else:
        tk.Label(result_frame, text="✗ فشل في الاتصال بقاعدة البيانات",
                font=('Arial', 14), bg='white', fg='red').pack(pady=10)
        tk.Label(result_frame, text="تأكد من تشغيل MySQL Server",
                font=('Arial', 12), bg='white', fg='orange').pack(pady=5)

def logout_click():
    result = messagebox.askyesno("تسجيل خروج", "هل تريد تسجيل الخروج؟")
    if result:
        root.destroy()
        create_login_window()

def create_login_window():
    global root, username_var, password_var
    
    root = tk.Tk()
    root.title("صيدلية الشفاء - تسجيل الدخول")
    root.geometry("500x400")
    root.configure(bg='#f0f0f0')
    
    # توسيط النافذة
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (500 // 2)
    y = (root.winfo_screenheight() // 2) - (400 // 2)
    root.geometry(f'500x400+{x}+{y}')
    
    # إطار تسجيل الدخول
    login_frame = tk.Frame(root, bg='white', relief='raised', bd=3)
    login_frame.place(relx=0.5, rely=0.5, anchor='center', width=350, height=300)
    
    # العنوان
    tk.Label(login_frame, text="صيدلية الشفاء", font=('Arial', 18, 'bold'),
             bg='white', fg='#27ae60').pack(pady=20)
    
    tk.Label(login_frame, text="نظام إدارة شامل", font=('Arial', 12),
             bg='white', fg='#2c3e50').pack(pady=5)
    
    tk.Label(login_frame, text="تسجيل الدخول", font=('Arial', 14, 'bold'),
             bg='white', fg='#2c3e50').pack(pady=15)
    
    # حقول الإدخال
    username_var = tk.StringVar()
    password_var = tk.StringVar()
    
    tk.Label(login_frame, text="اسم المستخدم:", font=('Arial', 11),
             bg='white').pack()
    tk.Entry(login_frame, textvariable=username_var, font=('Arial', 12),
             width=20).pack(pady=5)
    
    tk.Label(login_frame, text="كلمة المرور:", font=('Arial', 11),
             bg='white').pack()
    tk.Entry(login_frame, textvariable=password_var, font=('Arial', 12),
             width=20, show='*').pack(pady=5)
    
    # زر تسجيل الدخول
    tk.Button(login_frame, text="دخول", command=login,
              bg='#27ae60', fg='white', font=('Arial', 12, 'bold'),
              width=15, height=1).pack(pady=20)
    
    # معلومات تسجيل الدخول
    info_text = "بيانات تسجيل الدخول:\nالمدير: admin / admin123\nالمستخدم: user / user123"
    tk.Label(login_frame, text=info_text, font=('Arial', 9),
             bg='white', fg='#7f8c8d').pack()
    
    root.mainloop()

if __name__ == "__main__":
    print("تم تشغيل نظام صيدلية الشفاء")
    print("بيانات تسجيل الدخول:")
    print("  المدير: admin / admin123")
    print("  المستخدم: user / user123")
    
    create_login_window()
