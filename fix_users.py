# -*- coding: utf-8 -*-
"""
إصلاح سريع لجدول المستخدمين
"""

import mysql.connector
from mysql.connector import Error

def fix_users_table():
    """إصلاح جدول المستخدمين"""
    try:
        # الاتصال بقاعدة البيانات
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='pharmacy_db'
        )
        
        cursor = connection.cursor()
        
        print("🔧 إصلاح جدول المستخدمين...")
        
        # حذف جدول المستخدمين الحالي
        cursor.execute("DROP TABLE IF EXISTS users")
        print("🗑️ تم حذف الجدول القديم")
        
        # إنشاء جدول المستخدمين الجديد
        create_users_table = """
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'user') NOT NULL DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        cursor.execute(create_users_table)
        print("✅ تم إنشاء جدول المستخدمين الجديد")
        
        # إدراج المستخدمين الافتراضيين
        users_data = [
            ('admin', 'admin123', 'admin'),
            ('user', 'user123', 'user'),
            ('med', 'med123', 'user')
        ]
        
        insert_query = "INSERT INTO users (username, password, role) VALUES (%s, %s, %s)"
        cursor.executemany(insert_query, users_data)
        
        print("👤 تم إدراج المستخدمين:")
        print("   👑 admin (مدير النظام)")
        print("   👤 user (مستخدم محدود)")
        print("   👤 med (مستخدم محدود)")
        
        # التحقق من البيانات
        cursor.execute("SELECT username, role FROM users")
        users = cursor.fetchall()
        
        print("\n📋 المستخدمين في قاعدة البيانات:")
        for username, role in users:
            role_name = "مدير النظام" if role == 'admin' else "مستخدم محدود"
            print(f"   👤 {username} - {role_name}")
        
        # اختبار تسجيل الدخول
        print("\n🧪 اختبار تسجيل الدخول:")
        
        # اختبار المدير
        cursor.execute("SELECT username, role FROM users WHERE username = %s AND password = %s", 
                      ('admin', 'admin123'))
        admin_result = cursor.fetchone()
        if admin_result:
            print("✅ المدير - تسجيل الدخول ناجح")
        else:
            print("❌ المدير - فشل في تسجيل الدخول")
        
        # اختبار المستخدم
        cursor.execute("SELECT username, role FROM users WHERE username = %s AND password = %s", 
                      ('user', 'user123'))
        user_result = cursor.fetchone()
        if user_result:
            print("✅ المستخدم - تسجيل الدخول ناجح")
        else:
            print("❌ المستخدم - فشل في تسجيل الدخول")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("\n✅ تم إصلاح جدول المستخدمين بنجاح!")
        return True
        
    except Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='pharmacy_db'
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        if result:
            print("✅ الاتصال بقاعدة البيانات ناجح")
            return True
        else:
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
            
    except Error as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح سريع لجدول المستخدمين - صيدلية الشفاء")
    print("=" * 60)
    
    # اختبار الاتصال
    if not test_database_connection():
        print("\n❌ فشل في الاتصال بقاعدة البيانات")
        print("💡 تأكد من تشغيل MySQL وتشغيل setup.py أولاً")
        return
    
    # إصلاح جدول المستخدمين
    if fix_users_table():
        print("\n🚀 النظام جاهز للاستخدام!")
        print("\n📝 بيانات تسجيل الدخول:")
        print("👑 المدير: admin / admin123")
        print("👤 المستخدم المحدود: user / user123")
        print("👤 المستخدم الطبي: med / med123")
        print("\n💡 تشغيل النظام: python main.py")
    else:
        print("\n❌ فشل في إصلاح جدول المستخدمين")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
