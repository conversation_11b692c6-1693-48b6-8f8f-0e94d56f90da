# -*- coding: utf-8 -*-
"""
اختبار بسيط للواجهة
"""

import tkinter as tk
from tkinter import messagebox
import mysql.connector

def connect_db():
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root", 
            password="",
            database="pharmacy_db"
        )
    except:
        return None

def test_db():
    db = connect_db()
    if db:
        cursor = db.cursor()
        cursor.execute("SELECT COUNT(*) FROM users")
        count = cursor.fetchone()[0]
        cursor.close()
        db.close()
        messagebox.showinfo("نتيجة الاختبار", f"عدد المستخدمين: {count}")
    else:
        messagebox.showerror("خطأ", "فشل في الاتصال بقاعدة البيانات")

def login():
    username = entry1.get()
    password = entry2.get()
    
    if username == "admin" and password == "admin123":
        messagebox.showinfo("نجح", "تم تسجيل الدخول بنجاح!")
        show_main()
    else:
        messagebox.showerror("خطأ", "بيانات خاطئة")

def show_main():
    # إخفاء شاشة تسجيل الدخول
    login_frame.pack_forget()
    
    # إظهار النظام الرئيسي
    main_frame.pack(fill="both", expand=True)

def show_dashboard():
    content_frame.pack_forget()
    content_frame.pack(fill="both", expand=True)
    
    for widget in content_frame.winfo_children():
        widget.destroy()
    
    tk.Label(content_frame, text="لوحة التحكم", font=('Arial', 20, 'bold')).pack(pady=50)
    tk.Label(content_frame, text="مرحباً بك في النظام", font=('Arial', 14)).pack()

def show_inventory():
    content_frame.pack_forget()
    content_frame.pack(fill="both", expand=True)
    
    for widget in content_frame.winfo_children():
        widget.destroy()
    
    tk.Label(content_frame, text="إدارة المخزون", font=('Arial', 20, 'bold')).pack(pady=50)
    tk.Label(content_frame, text="صفحة المخزون", font=('Arial', 14)).pack()

def show_sales():
    content_frame.pack_forget()
    content_frame.pack(fill="both", expand=True)
    
    for widget in content_frame.winfo_children():
        widget.destroy()
    
    tk.Label(content_frame, text="إدارة المبيعات", font=('Arial', 20, 'bold')).pack(pady=50)
    tk.Label(content_frame, text="صفحة المبيعات", font=('Arial', 14)).pack()

def logout():
    main_frame.pack_forget()
    login_frame.pack(fill="both", expand=True)
    entry1.delete(0, tk.END)
    entry2.delete(0, tk.END)

# إنشاء النافذة الرئيسية
root = tk.Tk()
root.title("صيدلية الشفاء - اختبار")
root.geometry("800x600")
root.configure(bg='#f0f0f0')

# شاشة تسجيل الدخول
login_frame = tk.Frame(root, bg='#f0f0f0')

# إطار تسجيل الدخول
login_box = tk.Frame(login_frame, bg='white', relief='raised', bd=2)
login_box.place(relx=0.5, rely=0.5, anchor='center', width=300, height=250)

tk.Label(login_box, text="صيدلية الشفاء", font=('Arial', 16, 'bold'), bg='white').pack(pady=20)
tk.Label(login_box, text="تسجيل الدخول", font=('Arial', 12), bg='white').pack(pady=10)

tk.Label(login_box, text="اسم المستخدم:", bg='white').pack()
entry1 = tk.Entry(login_box, font=('Arial', 12))
entry1.pack(pady=5)

tk.Label(login_box, text="كلمة المرور:", bg='white').pack()
entry2 = tk.Entry(login_box, font=('Arial', 12), show='*')
entry2.pack(pady=5)

tk.Button(login_box, text="دخول", command=login, bg='#27ae60', fg='white', 
          font=('Arial', 12, 'bold')).pack(pady=15)

tk.Label(login_box, text="admin / admin123", font=('Arial', 9), bg='white').pack()

# النظام الرئيسي (مخفي في البداية)
main_frame = tk.Frame(root, bg='#f0f0f0')

# الشريط العلوي
top_bar = tk.Frame(main_frame, bg='#27ae60', height=50)
top_bar.pack(side='top', fill='x')
top_bar.pack_propagate(False)

tk.Label(top_bar, text="صيدلية الشفاء - نظام إدارة شامل", 
         font=('Arial', 14, 'bold'), bg='#27ae60', fg='white').pack(side='left', padx=20, pady=15)

# القائمة الجانبية
sidebar = tk.Frame(main_frame, bg='#2c3e50', width=200)
sidebar.pack(side='left', fill='y')
sidebar.pack_propagate(False)

tk.Label(sidebar, text="القائمة", font=('Arial', 14, 'bold'), 
         bg='#2c3e50', fg='white').pack(pady=20)

buttons = [
    ("لوحة التحكم", show_dashboard),
    ("المخزون", show_inventory), 
    ("المبيعات", show_sales),
    ("اختبار قاعدة البيانات", test_db),
    ("تسجيل خروج", logout)
]

for text, command in buttons:
    btn = tk.Button(sidebar, text=text, command=command,
                   bg='#2c3e50', fg='white', font=('Arial', 11),
                   relief='flat', width=18, pady=8)
    btn.pack(pady=2, padx=10, fill='x')

# المحتوى الرئيسي
content_frame = tk.Frame(main_frame, bg='#f0f0f0')
content_frame.pack(side='right', fill='both', expand=True)

# بدء النظام بشاشة تسجيل الدخول
login_frame.pack(fill="both", expand=True)

print("تم تشغيل النظام")
print("بيانات تسجيل الدخول: admin / admin123")

root.mainloop()
