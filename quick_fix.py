# -*- coding: utf-8 -*-
"""
إصلاح سريع للأخطاء في النظام
Quick Fix for System Errors
"""

import mysql.connector

def connect_db():
    """الاتصال بقاعدة البيانات"""
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="pharmacy_db"
        )
    except mysql.connector.Error as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def fix_users_table():
    """إصلاح جدول المستخدمين"""
    db = connect_db()
    if not db:
        return False

    cursor = db.cursor()

    try:
        print("🔧 إصلاح جدول المستخدمين...")
        
        # التحقق من وجود الجدول
        cursor.execute("SHOW TABLES LIKE 'users'")
        if not cursor.fetchone():
            print("📋 إنشاء جدول المستخدمين...")
            cursor.execute("""
                CREATE TABLE users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    role VARCHAR(20) DEFAULT 'user',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            db.commit()
            print("✅ تم إنشاء جدول المستخدمين")
        
        # التحقق من وجود المدير
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        admin_count = cursor.fetchone()[0]
        
        if admin_count == 0:
            print("👑 إنشاء حساب المدير...")
            cursor.execute("""
                INSERT INTO users (username, password, role)
                VALUES ('admin', 'admin123', 'admin')
            """)
            db.commit()
            print("✅ تم إنشاء حساب المدير")
        
        # التحقق من وجود المستخدم المحدود
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'user'")
        user_count = cursor.fetchone()[0]
        
        if user_count == 0:
            print("👤 إنشاء المستخدم المحدود...")
            cursor.execute("""
                INSERT INTO users (username, password, role)
                VALUES ('user', 'user123', 'user')
            """)
            db.commit()
            print("✅ تم إنشاء المستخدم المحدود")
        
        # عرض المستخدمين الحاليين
        cursor.execute("SELECT username, role FROM users")
        users = cursor.fetchall()
        
        print("\n📋 المستخدمين الحاليين:")
        for username, role in users:
            role_name = "مدير النظام" if role == "admin" else "مستخدم محدود"
            print(f"   👤 {username} - {role_name}")
        
        return True

    except mysql.connector.Error as e:
        print(f"❌ خطأ في إصلاح جدول المستخدمين: {e}")
        return False
    finally:
        cursor.close()
        db.close()

def test_login():
    """اختبار تسجيل الدخول"""
    db = connect_db()
    if not db:
        return False

    cursor = db.cursor()

    try:
        print("\n🧪 اختبار تسجيل الدخول:")
        
        # اختبار المدير
        cursor.execute("SELECT username, role FROM users WHERE username = 'admin' AND password = 'admin123'")
        admin_result = cursor.fetchone()
        
        if admin_result:
            print("✅ المدير - تسجيل الدخول ناجح")
        else:
            print("❌ المدير - فشل في تسجيل الدخول")
        
        # اختبار المستخدم المحدود
        cursor.execute("SELECT username, role FROM users WHERE username = 'user' AND password = 'user123'")
        user_result = cursor.fetchone()
        
        if user_result:
            print("✅ المستخدم المحدود - تسجيل الدخول ناجح")
        else:
            print("❌ المستخدم المحدود - فشل في تسجيل الدخول")
        
        return True

    except mysql.connector.Error as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False
    finally:
        cursor.close()
        db.close()

def main():
    """الدالة الرئيسية"""
    print("⚡ إصلاح سريع - صيدلية الشفاء")
    print("=" * 40)
    
    # إصلاح جدول المستخدمين
    if fix_users_table():
        print("✅ تم إصلاح جدول المستخدمين بنجاح")
    else:
        print("❌ فشل في إصلاح جدول المستخدمين")
        return
    
    # اختبار تسجيل الدخول
    if test_login():
        print("✅ تم اختبار تسجيل الدخول بنجاح")
    else:
        print("❌ فشل في اختبار تسجيل الدخول")
        return
    
    print("\n" + "=" * 40)
    print("🚀 النظام جاهز للاستخدام!")
    print("\n📝 بيانات تسجيل الدخول:")
    print("👑 المدير: admin / admin123")
    print("👤 المستخدم المحدود: user / user123")
    print("\n💡 تشغيل النظام: python main.py")

if __name__ == "__main__":
    main()
