# -*- coding: utf-8 -*-
"""
اختبار بسيط جداً لـ tkinter
"""

import tkinter as tk
from tkinter import messagebox

def test_button():
    messagebox.showinfo("اختبار", "الزر يعمل بشكل صحيح!")

def test_login():
    username = entry1.get()
    password = entry2.get()
    
    if username == "admin" and password == "admin123":
        messagebox.showinfo("نجح", f"مرحباً {username}!")
        # إخفاء شاشة تسجيل الدخول
        login_frame.pack_forget()
        # إظهار الشاشة الرئيسية
        main_frame.pack(fill="both", expand=True)
    else:
        messagebox.showerror("خطأ", "بيانات خاطئة!")

def logout():
    # إخفاء الشاشة الرئيسية
    main_frame.pack_forget()
    # مسح الحقول
    entry1.delete(0, tk.END)
    entry2.delete(0, tk.END)
    # إظهار شاشة تسجيل الدخول
    login_frame.pack(fill="both", expand=True)

# إنشاء النافذة
root = tk.Tk()
root.title("اختبار بسيط")
root.geometry("800x600")
root.configure(bg="white")

# شاشة تسجيل الدخول
login_frame = tk.Frame(root, bg="lightblue")

tk.Label(login_frame, text="🏥 اختبار النظام", font=("Arial", 20, "bold"), bg="lightblue").pack(pady=20)

tk.Label(login_frame, text="اسم المستخدم:", font=("Arial", 12), bg="lightblue").pack(pady=5)
entry1 = tk.Entry(login_frame, font=("Arial", 12), width=20)
entry1.pack(pady=5)

tk.Label(login_frame, text="كلمة المرور:", font=("Arial", 12), bg="lightblue").pack(pady=5)
entry2 = tk.Entry(login_frame, font=("Arial", 12), width=20, show="*")
entry2.pack(pady=5)

tk.Button(login_frame, text="دخول", command=test_login, font=("Arial", 12), 
          bg="green", fg="white", width=15, height=2).pack(pady=20)

tk.Label(login_frame, text="admin / admin123", font=("Arial", 10), bg="lightblue").pack(pady=5)

# الشاشة الرئيسية
main_frame = tk.Frame(root, bg="lightgreen")

tk.Label(main_frame, text="🎉 مرحباً في النظام!", font=("Arial", 20, "bold"), bg="lightgreen").pack(pady=50)

tk.Button(main_frame, text="اختبار", command=test_button, font=("Arial", 12), 
          bg="blue", fg="white", width=15, height=2).pack(pady=10)

tk.Button(main_frame, text="خروج", command=logout, font=("Arial", 12), 
          bg="red", fg="white", width=15, height=2).pack(pady=10)

# بدء النظام بشاشة تسجيل الدخول
login_frame.pack(fill="both", expand=True)

print("🚀 تم تشغيل الاختبار البسيط")
print("📝 admin / admin123")

root.mainloop()
