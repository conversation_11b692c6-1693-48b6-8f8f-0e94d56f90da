# -*- coding: utf-8 -*-
import tkinter as tk
from tkinter import ttk, messagebox
import mysql.connector
from datetime import datetime
import sys
from validation import validate_inventory_data
from interface_manager import register_inventory_interface

# إعداد الترميز للنصوص العربية
if sys.platform.startswith('win'):
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'Arabic_Saudi Arabia.1256')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'ar_SA.UTF-8')
        except:
            pass

def connect_db():
    return mysql.connector.connect(
        host="localhost",
        user="root",
        password="",
        database="pharmacy_db"
    )

def Inventory_page(root):
    # إطار رئيسي بتصميم محسن
    frame = tk.Frame(root, bg="#f8f9fa")

    # إطار العنوان مع تصميم أنيق
    header_frame = tk.Frame(frame, bg="#2c3e50", height=60)
    header_frame.grid(row=0, column=0, columnspan=4, sticky="ew", pady=(0, 20))
    header_frame.grid_propagate(False)

    tk.Label(header_frame, text="📦 إدارة المخزون", font=("Arial", 18, "bold"),
             bg="#2c3e50", fg="white").pack(expand=True)

    # إطار النموذج مع تصميم محسن
    form_frame = tk.Frame(frame, bg="#ffffff", relief="raised", bd=2)
    form_frame.grid(row=1, column=0, columnspan=4, sticky="ew", padx=20, pady=10)

    tk.Label(form_frame, text="📝 إضافة/تعديل منتج", font=("Arial", 14, "bold"),
             bg="#ffffff", fg="#2c3e50").grid(row=0, column=0, columnspan=2, pady=10)

    labels = ["اسم المنتج", "الفئة", "سعر الجملة", "سعر البيع", "الكمية", "تاريخ الانتهاء (MM/YYYY)"]
    entries = []

    for i, label in enumerate(labels):
        tk.Label(form_frame, text=label + ":", font=("Arial", 11),
                bg="#ffffff", fg="#34495e").grid(row=i+1, column=0, sticky="e", pady=8, padx=10)
        entry = tk.Entry(form_frame, width=25, font=("Arial", 11), relief="solid", bd=1)
        entry.grid(row=i+1, column=1, pady=8, padx=10, sticky="w")
        entries.append(entry)

    name_entry, cat_entry, wp_entry, sp_entry, qty_entry, date_entry = entries

    # إطار الأزرار مع تصميم محسن
    buttons_frame = tk.Frame(form_frame, bg="#ffffff")
    buttons_frame.grid(row=7, column=0, columnspan=2, pady=15)

    # إطار جدول البيانات
    table_frame = tk.Frame(frame, bg="#ffffff", relief="raised", bd=2)
    table_frame.grid(row=3, column=0, columnspan=4, sticky="ew", padx=20, pady=10)

    tk.Label(table_frame, text="📊 قائمة المنتجات", font=("Arial", 14, "bold"),
             bg="#ffffff", fg="#2c3e50").pack(pady=10)

    # تحسين تصميم الجدول
    style = ttk.Style()
    style.configure("Treeview.Heading", font=("Arial", 11, "bold"))
    style.configure("Treeview", font=("Arial", 10))

    tree = ttk.Treeview(table_frame, columns=("name", "cat", "wp", "sp", "qty", "exp"),
                       show="headings", height=12)
    cols = ["اسم المنتج", "الفئة", "سعر الجملة", "سعر البيع", "الكمية", "تاريخ الانتهاء"]
    for i, col in enumerate(cols):
        tree.heading(i, text=col)
        tree.column(i, width=140, anchor="center")

    # إضافة شريط التمرير
    scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
    tree.configure(yscrollcommand=scrollbar.set)

    tree.pack(side="left", fill="both", expand=True, padx=10, pady=(0, 10))
    scrollbar.pack(side="right", fill="y", pady=(0, 10))

    # إطار الإحصائيات مع تصميم محسن
    stats_frame = tk.Frame(frame, bg="#ffffff", relief="raised", bd=2)
    stats_frame.grid(row=4, column=0, columnspan=4, sticky="ew", padx=20, pady=10)

    tk.Label(stats_frame, text="📈 إحصائيات المخزون", font=("Arial", 14, "bold"),
             bg="#ffffff", fg="#2c3e50").grid(row=0, column=0, columnspan=4, pady=10)

    # تسميات الإحصائيات مع ألوان محسنة
    stock_lbl = tk.Label(stats_frame, text="0", font=("Arial", 12, "bold"),
                        fg="#e74c3c", bg="#ffffff")
    cp_lbl = tk.Label(stats_frame, text="Ghc 0.00", font=("Arial", 12, "bold"),
                     fg="#3498db", bg="#ffffff")
    sp_lbl = tk.Label(stats_frame, text="Ghc 0.00", font=("Arial", 12, "bold"),
                     fg="#9b59b6", bg="#ffffff")
    gp_lbl = tk.Label(stats_frame, text="Ghc 0.00", font=("Arial", 12, "bold"),
                     fg="#27ae60", bg="#ffffff")

    def load_inventory():
        for item in tree.get_children():
            tree.delete(item)

        db = connect_db()
        cursor = db.cursor()
        cursor.execute("SELECT * FROM inventory")
        rows = cursor.fetchall()

        total_cp = total_sp = total_stock = 0

        for row in rows:
            tree.insert("", "end", values=row[1:])
            total_cp += row[3] * row[5]
            total_sp += row[4] * row[5]
            total_stock += row[5]

        cursor.close()
        db.close()

        stock_lbl.config(text=f"{total_stock}")
        cp_lbl.config(text=f"Ghc {total_cp:.2f}")
        sp_lbl.config(text=f"Ghc {total_sp:.2f}")
        gp_lbl.config(text=f"Ghc {total_sp - total_cp:.2f}")

    def add_item():
        # التحقق من صحة البيانات
        valid, errors = validate_inventory_data(
            name_entry.get(),
            cat_entry.get(),
            wp_entry.get(),
            sp_entry.get(),
            qty_entry.get(),
            date_entry.get()
        )

        if not valid:
            error_message = "يرجى تصحيح الأخطاء التالية:\n\n" + "\n".join(f"• {error}" for error in errors)
            messagebox.showerror("❌ خطأ في البيانات", error_message)
            return

        try:
            db = connect_db()
            cursor = db.cursor()

            # التحقق من عدم وجود منتج بنفس الاسم
            cursor.execute("SELECT id FROM inventory WHERE product_name = %s", (name_entry.get().strip(),))
            if cursor.fetchone():
                messagebox.showerror("❌ خطأ", "يوجد منتج بنفس الاسم مسبقاً")
                cursor.close()
                db.close()
                return

            query = """
            INSERT INTO inventory (product_name, category, wholesale_price, selling_price, quantity, expiry_date)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            values = (
                name_entry.get().strip(),
                cat_entry.get().strip(),
                float(wp_entry.get().strip()),
                float(sp_entry.get().strip()),
                int(qty_entry.get().strip()),
                datetime.strptime(date_entry.get().strip(), "%m/%Y").replace(day=1)
            )
            cursor.execute(query, values)
            db.commit()
            cursor.close()
            db.close()
            load_inventory()
            clear_fields()
            messagebox.showinfo("✅ تم", "تمت إضافة المنتج بنجاح!")
        except Exception as e:
            messagebox.showerror("❌ خطأ", f"حدث خطأ في قاعدة البيانات: {str(e)}")

    def update_item():
        selected = tree.focus()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للتحديث")
            return

        values = tree.item(selected, 'values')
        product_name = values[0]

        # التحقق من صحة البيانات
        valid, errors = validate_inventory_data(
            name_entry.get(),
            cat_entry.get(),
            wp_entry.get(),
            sp_entry.get(),
            qty_entry.get(),
            date_entry.get()
        )

        if not valid:
            error_message = "يرجى تصحيح الأخطاء التالية:\n\n" + "\n".join(f"• {error}" for error in errors)
            messagebox.showerror("❌ خطأ في البيانات", error_message)
            return

        try:
            db = connect_db()
            cursor = db.cursor()

            # التحقق من عدم وجود منتج آخر بنفس الاسم الجديد
            if name_entry.get().strip() != product_name:
                cursor.execute("SELECT id FROM inventory WHERE product_name = %s", (name_entry.get().strip(),))
                if cursor.fetchone():
                    messagebox.showerror("❌ خطأ", "يوجد منتج آخر بنفس الاسم")
                    cursor.close()
                    db.close()
                    return

            query = """
            UPDATE inventory SET product_name=%s, category=%s, wholesale_price=%s,
            selling_price=%s, quantity=%s, expiry_date=%s
            WHERE product_name=%s
            """
            cursor.execute(query, (
                name_entry.get().strip(),
                cat_entry.get().strip(),
                float(wp_entry.get().strip()),
                float(sp_entry.get().strip()),
                int(qty_entry.get().strip()),
                datetime.strptime(date_entry.get().strip(), "%m/%Y").replace(day=1),
                product_name
            ))
            db.commit()
            cursor.close()
            db.close()
            load_inventory()
            clear_fields()
            messagebox.showinfo("✅ تم", "تم تحديث المنتج بنجاح!")
        except Exception as e:
            messagebox.showerror("❌ خطأ", f"حدث خطأ في قاعدة البيانات: {str(e)}")

    def remove_item():
        selected = tree.focus()
        if not selected:
            return
        values = tree.item(selected, 'values')
        product_name = values[0]

        db = connect_db()
        cursor = db.cursor()
        cursor.execute("DELETE FROM inventory WHERE product_name = %s", (product_name,))
        db.commit()
        cursor.close()
        db.close()
        load_inventory()
        clear_fields()
        messagebox.showinfo("✅ تم", "تم حذف المنتج بنجاح!")

    def clear_fields():
        name_entry.delete(0, tk.END)
        cat_entry.delete(0, tk.END)
        wp_entry.delete(0, tk.END)
        sp_entry.delete(0, tk.END)
        qty_entry.delete(0, tk.END)
        date_entry.delete(0, tk.END)

    def on_select(event):
        selected = tree.focus()
        if not selected:
            return
        values = tree.item(selected, 'values')
        clear_fields()

        name_entry.insert(0, values[0])
        cat_entry.insert(0, values[1])
        wp_entry.insert(0, values[2])
        sp_entry.insert(0, values[3])
        qty_entry.insert(0, values[4])
        date_entry.insert(0, datetime.strptime(values[5], "%Y-%m-%d").strftime("%m/%Y"))

    tree.bind("<<TreeviewSelect>>", on_select)

    # أزرار محسنة مع أيقونات وألوان جذابة
    tk.Button(buttons_frame, text="➕ إضافة منتج", bg="#27ae60", fg="white",
             font=("Arial", 11, "bold"), width=15, height=2,
             command=add_item, relief="flat", cursor="hand2").pack(side="left", padx=5)

    tk.Button(buttons_frame, text="✏️ تحديث", bg="#3498db", fg="white",
             font=("Arial", 11, "bold"), width=15, height=2,
             command=update_item, relief="flat", cursor="hand2").pack(side="left", padx=5)

    tk.Button(buttons_frame, text="🗑️ حذف", bg="#e74c3c", fg="white",
             font=("Arial", 11, "bold"), width=15, height=2,
             command=remove_item, relief="flat", cursor="hand2").pack(side="left", padx=5)

    tk.Button(buttons_frame, text="🔄 مسح الحقول", bg="#95a5a6", fg="white",
             font=("Arial", 11, "bold"), width=15, height=2,
             command=clear_fields, relief="flat", cursor="hand2").pack(side="left", padx=5)

    # تخطيط الإحصائيات بشكل أفضل
    tk.Label(stats_frame, text="📦 إجمالي المخزون:", font=("Arial", 11, "bold"),
             bg="#ffffff", fg="#2c3e50").grid(row=1, column=0, sticky="e", padx=10, pady=5)
    stock_lbl.grid(row=1, column=1, sticky="w", padx=10, pady=5)

    tk.Label(stats_frame, text="💰 إجمالي سعر الشراء:", font=("Arial", 11, "bold"),
             bg="#ffffff", fg="#2c3e50").grid(row=1, column=2, sticky="e", padx=10, pady=5)
    cp_lbl.grid(row=1, column=3, sticky="w", padx=10, pady=5)

    tk.Label(stats_frame, text="💵 إجمالي سعر البيع:", font=("Arial", 11, "bold"),
             bg="#ffffff", fg="#2c3e50").grid(row=2, column=0, sticky="e", padx=10, pady=5)
    sp_lbl.grid(row=2, column=1, sticky="w", padx=10, pady=5)

    tk.Label(stats_frame, text="📈 الربح المتوقع:", font=("Arial", 11, "bold"),
             bg="#ffffff", fg="#2c3e50").grid(row=2, column=2, sticky="e", padx=10, pady=5)
    gp_lbl.grid(row=2, column=3, sticky="w", padx=10, pady=5)

    load_inventory()

    # تسجيل واجهة المخزون في مدير الواجهات
    register_inventory_interface(frame)

    return frame
