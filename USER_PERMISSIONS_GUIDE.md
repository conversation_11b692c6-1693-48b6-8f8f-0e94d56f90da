# 👥 دليل نظام المستخدمين والصلاحيات المحدودة

## 🎯 **الهدف:**
تطوير نظام مستخدمين متقدم مع صلاحيات محدودة وحذف النصوص الافتراضية من حقول تسجيل الدخول.

## 🔐 **أنواع المستخدمين:**

### **1. 👑 مدير النظام (Admin)**
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **الصلاحيات الكاملة**:
  - ✅ لوحة التحكم
  - ✅ إدارة المخزون
  - ✅ نظام المبيعات
  - ✅ إدارة الفواتير
  - ✅ إدارة المستخدمين
  - ✅ التقارير والإحصائيات

### **2. 👤 مستخدم محدود (User)**
- **اسم المستخدم**: user
- **كلمة المرور**: user123
- **الصلاحيات المحدودة**:
  - ✅ لوحة التحكم
  - ✅ إدارة المخزون
  - ✅ نظام المبيعات
  - ❌ إدارة الفواتير (للمدير فقط)
  - ❌ إدارة المستخدمين (للمدير فقط)
  - ❌ التقارير المتقدمة (للمدير فقط)

## 🔧 **التحديثات التقنية:**

### **1. حقول تسجيل الدخول المحسنة:**

#### **قبل التحديث:**
```python
username_entry.insert(0, "اسم المستخدم")  # نص ثابت
password_entry = tk.Entry(show="*")         # بدون placeholder
```

#### **بعد التحديث:**
```python
def create_placeholder_entry(parent, placeholder_text, show_char=None):
    """إنشاء حقل إدخال مع نص توضيحي يختفي عند التركيز"""
    entry = tk.Entry(parent, show=show_char)
    entry.insert(0, placeholder_text)
    entry.config(fg='gray')
    
    def on_focus_in(event):
        if entry.get() == placeholder_text:
            entry.delete(0, tk.END)
            entry.config(fg='black')
    
    def on_focus_out(event):
        if entry.get() == '':
            entry.insert(0, placeholder_text)
            entry.config(fg='gray')
    
    entry.bind('<FocusIn>', on_focus_in)
    entry.bind('<FocusOut>', on_focus_out)
    
    return entry
```

### **2. نظام الصلاحيات:**

#### **تعريف الأدوار:**
```python
USER_ROLES = {
    'admin': {
        'name_ar': 'مدير النظام',
        'permissions': ['dashboard', 'inventory', 'sales', 'invoice', 'users', 'reports'],
        'description': 'صلاحيات كاملة لجميع أجزاء النظام'
    },
    'user': {
        'name_ar': 'مستخدم محدود',
        'permissions': ['dashboard', 'inventory', 'sales'],
        'description': 'صلاحيات محدودة للعمليات الأساسية فقط'
    }
}
```

#### **التحقق من الصلاحيات:**
```python
def check_permission(page_name):
    """التحقق من صلاحية المستخدم للوصول لصفحة معينة"""
    if current_user['role'] == 'admin':
        return True
    elif current_user['role'] == 'user':
        allowed_pages = ['Dashboard', 'Inventory', 'Sales']
        return page_name in allowed_pages
    return False
```

### **3. القائمة الجانبية الديناميكية:**

#### **قبل التحديث:**
```python
# قائمة ثابتة لجميع المستخدمين
menu_buttons = [
    ("لوحة التحكم", lambda: show_page("Dashboard")),
    ("المخزون", lambda: show_page("Inventory")),
    ("المبيعات", lambda: show_page("Sales")),
    ("الفواتير", lambda: show_page("Invoice")),      # متاح للجميع
    ("المستخدمين", lambda: show_page("Users")),      # متاح للجميع
]
```

#### **بعد التحديث:**
```python
def create_sidebar_menu():
    """إنشاء القائمة الجانبية حسب صلاحيات المستخدم"""
    # أزرار أساسية (متاحة للجميع)
    basic_buttons = [
        ("لوحة التحكم", lambda: show_page("Dashboard")),
        ("المخزون", lambda: show_page("Inventory")),
        ("المبيعات", lambda: show_page("Sales")),
    ]
    
    # أزرار المدير فقط
    admin_buttons = [
        ("الفواتير", lambda: show_page("Invoice")),
        ("المستخدمين", lambda: show_page("Users")),
    ]
    
    # إضافة الأزرار حسب الصلاحيات
    all_buttons = basic_buttons.copy()
    if current_user.get('role') == 'admin':
        all_buttons.extend(admin_buttons)
```

## 📱 **واجهة تسجيل الدخول المحسنة:**

### **الميزات الجديدة:**
1. **Placeholder ذكي**: النص التوضيحي يختفي عند التركيز
2. **تغيير اللون**: رمادي للنص التوضيحي، أسود للنص الفعلي
3. **التحقق المحسن**: فحص النص التوضيحي قبل المعالجة
4. **تجربة مستخدم أفضل**: واضح ومفهوم

### **مثال على الاستخدام:**
```
┌─────────────────────────────────────────┐
│ 🏥 صيدلية الشفاء                      │
│ نظام إدارة شامل ومتطور                │
│ تسجيل الدخول                           │
│                                         │
│ [اسم المستخدم]  ← رمادي، يختفي عند الكتابة │
│ [كلمة المرور]   ← رمادي، يختفي عند الكتابة │
│                                         │
│        [👤 دخول]                       │
└─────────────────────────────────────────┘
```

## 🛡️ **نظام الحماية:**

### **1. التحقق من الصلاحيات:**
```python
def show_page(page):
    # التحقق من الصلاحيات
    if not check_permission(page):
        messagebox.showerror("خطأ في الصلاحيات", 
                           f"ليس لديك صلاحية للوصول إلى صفحة {page}\n"
                           f"هذه الصفحة متاحة للمدير فقط")
        return
    
    # عرض الصفحة إذا كانت الصلاحية متاحة
    # ...
```

### **2. رسائل الخطأ الواضحة:**
```
❌ خطأ في الصلاحيات
ليس لديك صلاحية للوصول إلى صفحة الفواتير
هذه الصفحة متاحة للمدير فقط

[موافق]
```

### **3. منع الوصول المباشر:**
- المستخدم المحدود لا يرى أزرار الصفحات المحظورة
- حتى لو حاول الوصول برمجياً، سيتم منعه
- رسائل خطأ واضحة ومفهومة

## 👥 **إدارة المستخدمين المحسنة:**

### **الواجهة الجديدة:**
```
┌─────────────────────────────────────────────────────────────┐
│ 👥 إدارة المستخدمين والصلاحيات                           │
├─────────────────────────────────────────────────────────────┤
│ إضافة مستخدم جديد                                          │
│                                                             │
│ اسم المستخدم: [________] كلمة المرور: [________]           │
│ نوع المستخدم: [مستخدم محدود (user) ▼]                    │
│ الوصف: صلاحيات محدودة للعمليات الأساسية فقط               │
│                                                             │
│ [➕ إضافة] [🗑️ حذف] [🔄 مسح]                              │
├─────────────────────────────────────────────────────────────┤
│ قائمة المستخدمين                                          │
│                                                             │
│ الرقم │ اسم المستخدم │ النوع        │ الصلاحيات           │
│ ──────┼──────────────┼─────────────┼─────────────────────── │
│   1   │ admin        │ مدير النظام │ dashboard,inventory... │
│   2   │ user         │ مستخدم محدود│ dashboard,inventory... │
└─────────────────────────────────────────────────────────────┘
```

### **الميزات:**
- ✅ **واجهة حديثة** مع ألوان وأيقونات
- ✅ **وصف الصلاحيات** يتغير حسب النوع المختار
- ✅ **التحقق من البيانات** قبل الإضافة
- ✅ **منع حذف المدير الرئيسي**
- ✅ **عرض تفصيلي** للصلاحيات

## 🧪 **اختبار النظام:**

### **سيناريو 1: تسجيل دخول المدير**
```
1. اسم المستخدم: admin
2. كلمة المرور: admin123
3. النتيجة: ✅ دخول ناجح
4. القائمة الجانبية: جميع الأزرار متاحة
5. الوصول: جميع الصفحات متاحة
```

### **سيناريو 2: تسجيل دخول المستخدم المحدود**
```
1. اسم المستخدم: user
2. كلمة المرور: user123
3. النتيجة: ✅ دخول ناجح
4. القائمة الجانبية: أزرار محدودة فقط
5. الوصول: لوحة التحكم، المخزون، المبيعات فقط
```

### **سيناريو 3: محاولة وصول غير مصرح**
```
1. المستخدم المحدود يحاول الوصول للفواتير
2. النتيجة: ❌ رسالة خطأ
3. الرسالة: "ليس لديك صلاحية للوصول إلى صفحة الفواتير"
4. الإجراء: البقاء في الصفحة الحالية
```

## 📊 **مقارنة النظام:**

### **قبل التحديث:**
```
❌ نص ثابت في حقول تسجيل الدخول
❌ جميع المستخدمين لهم نفس الصلاحيات
❌ قائمة جانبية ثابتة للجميع
❌ لا يوجد تحكم في الوصول
❌ واجهة إدارة مستخدمين بسيطة
```

### **بعد التحديث:**
```
✅ حقول ذكية مع placeholder متفاعل
✅ نظام صلاحيات متقدم ومرن
✅ قائمة جانبية ديناميكية حسب الدور
✅ حماية قوية للصفحات الحساسة
✅ واجهة إدارة مستخدمين احترافية
```

## 🚀 **كيفية الاستخدام:**

### **للمدير:**
1. **تسجيل الدخول** بحساب admin
2. **إدارة المستخدمين** من القائمة الجانبية
3. **إضافة مستخدمين جدد** حسب الحاجة
4. **تحديد الصلاحيات** لكل مستخدم
5. **مراقبة النشاط** والوصول

### **للمستخدم المحدود:**
1. **تسجيل الدخول** بحساب user
2. **استخدام الوظائف المتاحة** فقط
3. **عدم محاولة الوصول** للصفحات المحظورة
4. **التركيز على المهام الأساسية**

### **إنشاء مستخدمين جدد:**
```python
# تشغيل ملف إنشاء المستخدم المحدود
python create_limited_user.py

# أو من خلال واجهة إدارة المستخدمين
1. تسجيل دخول كمدير
2. اختيار "المستخدمين"
3. ملء بيانات المستخدم الجديد
4. اختيار نوع المستخدم
5. الضغط على "إضافة مستخدم"
```

## 💡 **نصائح مهمة:**

### **للأمان:**
- ✅ **غيّر كلمات المرور الافتراضية**
- ✅ **استخدم كلمات مرور قوية**
- ✅ **راجع صلاحيات المستخدمين دورياً**
- ✅ **احذف المستخدمين غير المستخدمين**

### **للاستخدام:**
- ✅ **درّب المستخدمين على صلاحياتهم**
- ✅ **وضّح الصفحات المتاحة لكل مستخدم**
- ✅ **استخدم المستخدم المحدود للعمليات اليومية**
- ✅ **احتفظ بحساب المدير للإدارة فقط**

---

**👥 نظام مستخدمين متقدم مع حماية قوية وواجهة محسنة!**

**💡 تذكر**: الآن يمكنك إنشاء مستخدمين محدودي الصلاحيات للعمل اليومي مع الحفاظ على أمان النظام!
