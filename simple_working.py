# -*- coding: utf-8 -*-
"""
نظام صيدلية الشفاء - نسخة مبسطة ومضمونة العمل
"""

import tkinter as tk
from tkinter import messagebox
import mysql.connector

# الألوان
COLORS = {
    'primary': '#27ae60',
    'bg': '#f8f9fa',
    'sidebar': '#2c3e50',
    'white': '#ffffff',
    'text': '#2c3e50'
}

# متغير المستخدم الحالي
current_user = {'username': '', 'role': ''}

def connect_db():
    """الاتصال بقاعدة البيانات"""
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="pharmacy_db"
        )
    except:
        return None

def login():
    """تسجيل الدخول"""
    username = username_entry.get()
    password = password_entry.get()
    
    if not username or not password:
        messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
        return
    
    try:
        db = connect_db()
        if not db:
            messagebox.showerror("خطأ", "فشل في الاتصال بقاعدة البيانات")
            return
            
        cursor = db.cursor()
        cursor.execute("SELECT username, role FROM users WHERE username = %s AND password = %s", 
                      (username, password))
        result = cursor.fetchone()
        cursor.close()
        db.close()
        
        if result:
            current_user['username'] = result[0]
            current_user['role'] = result[1]
            
            role_name = "مدير النظام" if result[1] == 'admin' else "مستخدم محدود"
            messagebox.showinfo("مرحباً", f"أهلاً وسهلاً {result[0]}\nتم تسجيل الدخول بنجاح كـ {role_name}")
            
            show_main_system()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

def show_main_system():
    """عرض النظام الرئيسي"""
    # إخفاء شاشة تسجيل الدخول
    login_frame.pack_forget()
    
    # إنشاء الشريط العلوي
    top_frame = tk.Frame(root, bg=COLORS['primary'], height=50)
    top_frame.pack(side="top", fill="x")
    top_frame.pack_propagate(False)
    
    # عنوان النظام
    tk.Label(top_frame, text="صيدلية الشفاء - نظام إدارة شامل", 
             font=('Arial', 14, 'bold'), bg=COLORS['primary'], fg='white').pack(side="left", padx=20, pady=10)
    
    # معلومات المستخدم
    role_name = "مدير النظام" if current_user['role'] == 'admin' else "مستخدم محدود"
    tk.Label(top_frame, text=f"المستخدم: {current_user['username']} ({role_name})", 
             font=('Arial', 12), bg=COLORS['primary'], fg='white').pack(side="right", padx=20, pady=10)
    
    # إنشاء القائمة الجانبية
    sidebar = tk.Frame(root, bg=COLORS['sidebar'], width=200)
    sidebar.pack(side="left", fill="y")
    sidebar.pack_propagate(False)
    
    # شعار في القائمة
    tk.Label(sidebar, text="صيدلية الشفاء", font=('Arial', 16, 'bold'),
             bg=COLORS['sidebar'], fg='white').pack(pady=20)
    
    # أزرار القائمة
    buttons = [
        ("لوحة التحكم", show_dashboard),
        ("المخزون", show_inventory),
        ("المبيعات", show_sales),
    ]
    
    # أزرار المدير فقط
    if current_user['role'] == 'admin':
        buttons.extend([
            ("الفواتير", show_invoices),
            ("المستخدمين", show_users),
        ])
    
    buttons.append(("تسجيل خروج", logout))
    
    # إنشاء الأزرار
    for text, command in buttons:
        btn = tk.Button(sidebar, text=text, command=command,
                       bg=COLORS['sidebar'], fg='white', font=('Arial', 12),
                       relief="flat", anchor="w", padx=20, pady=10, width=18)
        btn.pack(fill="x", pady=2, padx=10)
    
    # إنشاء المحتوى الرئيسي
    global main_content
    main_content = tk.Frame(root, bg=COLORS['bg'])
    main_content.pack(side="right", fill="both", expand=True)
    
    # عرض لوحة التحكم
    show_dashboard()

def clear_content():
    """مسح المحتوى الرئيسي"""
    for widget in main_content.winfo_children():
        widget.destroy()

def show_dashboard():
    """عرض لوحة التحكم"""
    clear_content()
    
    # عنوان الصفحة
    tk.Label(main_content, text="لوحة التحكم", font=('Arial', 18, 'bold'),
             bg=COLORS['bg'], fg=COLORS['primary']).pack(pady=30)
    
    # رسالة ترحيب
    tk.Label(main_content, text=f"مرحباً بك {current_user['username']}", 
             font=('Arial', 14), bg=COLORS['bg'], fg=COLORS['text']).pack(pady=10)
    
    # إحصائيات
    stats_frame = tk.Frame(main_content, bg=COLORS['bg'])
    stats_frame.pack(pady=20)
    
    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            
            # عدد المنتجات
            cursor.execute("SELECT COUNT(*) FROM inventory")
            products_count = cursor.fetchone()[0]
            
            # عدد المبيعات
            cursor.execute("SELECT COUNT(*) FROM sales")
            sales_count = cursor.fetchone()[0]
            
            # عدد المستخدمين
            cursor.execute("SELECT COUNT(*) FROM users")
            users_count = cursor.fetchone()[0]
            
            cursor.close()
            db.close()
            
            # عرض الإحصائيات
            tk.Label(stats_frame, text=f"عدد المنتجات: {products_count}", 
                    font=('Arial', 12), bg=COLORS['bg']).pack(pady=5)
            tk.Label(stats_frame, text=f"عدد المبيعات: {sales_count}", 
                    font=('Arial', 12), bg=COLORS['bg']).pack(pady=5)
            tk.Label(stats_frame, text=f"عدد المستخدمين: {users_count}", 
                    font=('Arial', 12), bg=COLORS['bg']).pack(pady=5)
    except:
        tk.Label(stats_frame, text="النظام يعمل بشكل صحيح!", 
                font=('Arial', 12), bg=COLORS['bg']).pack(pady=10)

def show_inventory():
    """عرض المخزون"""
    clear_content()
    tk.Label(main_content, text="إدارة المخزون", font=('Arial', 18, 'bold'),
             bg=COLORS['bg'], fg=COLORS['primary']).pack(pady=50)
    tk.Label(main_content, text="صفحة المخزون متصلة بقاعدة البيانات", 
             font=('Arial', 12), bg=COLORS['bg']).pack()

def show_sales():
    """عرض المبيعات"""
    clear_content()
    tk.Label(main_content, text="إدارة المبيعات", font=('Arial', 18, 'bold'),
             bg=COLORS['bg'], fg=COLORS['primary']).pack(pady=50)
    tk.Label(main_content, text="صفحة المبيعات متصلة بقاعدة البيانات", 
             font=('Arial', 12), bg=COLORS['bg']).pack()

def show_invoices():
    """عرض الفواتير"""
    clear_content()
    tk.Label(main_content, text="إدارة الفواتير", font=('Arial', 18, 'bold'),
             bg=COLORS['bg'], fg=COLORS['primary']).pack(pady=50)
    tk.Label(main_content, text="صفحة الفواتير متصلة بقاعدة البيانات (للمدير فقط)", 
             font=('Arial', 12), bg=COLORS['bg']).pack()

def show_users():
    """عرض المستخدمين"""
    clear_content()
    tk.Label(main_content, text="إدارة المستخدمين", font=('Arial', 18, 'bold'),
             bg=COLORS['bg'], fg=COLORS['primary']).pack(pady=50)
    tk.Label(main_content, text="صفحة المستخدمين متصلة بقاعدة البيانات (للمدير فقط)", 
             font=('Arial', 12), bg=COLORS['bg']).pack()

def logout():
    """تسجيل الخروج"""
    result = messagebox.askyesno("تسجيل خروج", "هل تريد تسجيل الخروج من النظام؟")
    if result:
        # إعادة تشغيل النظام
        root.destroy()
        main()

def main():
    """الدالة الرئيسية"""
    global root, login_frame, username_entry, password_entry
    
    # إنشاء النافذة الرئيسية
    root = tk.Tk()
    root.title("صيدلية الشفاء - نظام إدارة شامل")
    root.geometry("1000x600")
    root.configure(bg=COLORS['bg'])
    
    # توسيط النافذة
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    # شاشة تسجيل الدخول
    login_frame = tk.Frame(root, bg=COLORS['bg'])
    login_frame.pack(fill="both", expand=True)
    
    # إطار تسجيل الدخول
    login_container = tk.Frame(login_frame, bg='white', relief="raised", bd=2)
    login_container.place(relx=0.5, rely=0.5, anchor="center", width=350, height=300)
    
    # العنوان
    tk.Label(login_container, text="صيدلية الشفاء", font=('Arial', 18, 'bold'),
             bg='white', fg=COLORS['primary']).pack(pady=20)
    
    tk.Label(login_container, text="نظام إدارة شامل", font=('Arial', 12),
             bg='white', fg=COLORS['text']).pack(pady=5)
    
    tk.Label(login_container, text="تسجيل الدخول", font=('Arial', 14, 'bold'),
             bg='white', fg=COLORS['text']).pack(pady=15)
    
    # حقول الإدخال
    tk.Label(login_container, text="اسم المستخدم:", font=('Arial', 10),
             bg='white', fg=COLORS['text']).pack()
    
    username_entry = tk.Entry(login_container, font=('Arial', 12), width=20)
    username_entry.pack(pady=5)
    
    tk.Label(login_container, text="كلمة المرور:", font=('Arial', 10),
             bg='white', fg=COLORS['text']).pack()
    
    password_entry = tk.Entry(login_container, font=('Arial', 12), width=20, show="*")
    password_entry.pack(pady=5)
    
    # زر تسجيل الدخول
    tk.Button(login_container, text="دخول", command=login,
              bg=COLORS['primary'], fg='white', font=('Arial', 12, 'bold'),
              width=15, height=1).pack(pady=15)
    
    # معلومات تسجيل الدخول
    tk.Label(login_container, text="المدير: admin / admin123\nالمستخدم: user / user123",
             font=('Arial', 9), bg='white', fg=COLORS['text']).pack()
    
    # ربط Enter
    username_entry.bind("<Return>", lambda e: password_entry.focus())
    password_entry.bind("<Return>", lambda e: login())
    
    # تركيز على حقل اسم المستخدم
    username_entry.focus()
    
    print("تم تشغيل نظام صيدلية الشفاء")
    print("بيانات تسجيل الدخول:")
    print("   المدير: admin / admin123")
    print("   المستخدم: user / user123")
    
    root.mainloop()

if __name__ == "__main__":
    main()
