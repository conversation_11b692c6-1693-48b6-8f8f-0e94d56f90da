# -*- coding: utf-8 -*-
"""
نسخة مبسطة من النظام الرئيسي - للاختبار
"""

import tkinter as tk
from tkinter import messagebox
import mysql.connector

# إعدادات الألوان والخطوط
COLORS = {
    'primary': '#27ae60',
    'bg_main': '#f8f9fa',
    'bg_card': '#ffffff',
    'bg_sidebar': '#2c3e50',
    'text_primary': '#2c3e50',
    'text_secondary': '#34495e',
    'text_white': '#ffffff',
    'btn_success': '#27ae60',
    'accent': '#3498db'
}

FONTS = {
    'title': ('Arial', 18, 'bold'),
    'heading': ('Arial', 14, 'bold'),
    'main': ('Arial', 12),
    'button': ('Arial', 11, 'bold')
}

ICONS = {
    'pharmacy': '🏥',
    'user': '👤',
    'dashboard': '📊',
    'inventory': '📦',
    'sales': '🛒',
    'invoice': '🧾',
    'users': '👥',
    'logout': '🚪'
}

# ==== الاتصال بقاعدة البيانات ====
def connect_db():
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="pharmacy_db"
        )
    except:
        return None

# ==== متغير المستخدم الحالي ====
current_user = {'username': '', 'role': ''}

# ==== دالة تسجيل الدخول ====
def login():
    username = username_entry.get()
    password = password_entry.get()
    
    # إزالة النص التوضيحي
    if username == "اسم المستخدم":
        username = ""
    if password == "كلمة المرور":
        password = ""
    
    if not username or not password:
        messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور.")
        return
    
    try:
        db = connect_db()
        if not db:
            messagebox.showerror("خطأ", "فشل في الاتصال بقاعدة البيانات.")
            return
            
        cursor = db.cursor()
        cursor.execute("SELECT username, role FROM users WHERE username = %s AND password = %s", 
                      (username, password))
        result = cursor.fetchone()
        cursor.close()
        db.close()
        
        if result:
            # حفظ معلومات المستخدم الحالي
            current_user['username'] = result[0]
            current_user['role'] = result[1]
            
            # رسالة ترحيب
            role_name = "مدير النظام" if result[1] == 'admin' else "مستخدم محدود"
            messagebox.showinfo("مرحباً", f"أهلاً وسهلاً {result[0]}\nتم تسجيل الدخول بنجاح كـ {role_name}")
            
            # إخفاء شاشة تسجيل الدخول وإظهار النظام الرئيسي
            login_frame.pack_forget()
            top_bar.pack(side="top", fill="x")
            dash_frame.pack(side="left", fill="y")
            
            # تحديث معلومات المستخدم
            update_user_info()
            
            # إظهار لوحة التحكم
            show_dashboard()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة.")
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

# ==== عرض لوحة التحكم ====
def show_dashboard():
    # إخفاء جميع الإطارات
    for widget in main_content.winfo_children():
        widget.destroy()
    
    # إنشاء محتوى لوحة التحكم
    dashboard_label = tk.Label(main_content, text="📊 لوحة التحكم", 
                              font=FONTS['title'], bg=COLORS['bg_main'], fg=COLORS['primary'])
    dashboard_label.pack(pady=20)
    
    welcome_label = tk.Label(main_content, text=f"مرحباً بك {current_user['username']}", 
                            font=FONTS['heading'], bg=COLORS['bg_main'], fg=COLORS['text_primary'])
    welcome_label.pack(pady=10)
    
    info_label = tk.Label(main_content, text="النظام يعمل بشكل صحيح!", 
                         font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['text_secondary'])
    info_label.pack(pady=10)

def update_user_info():
    """تحديث معلومات المستخدم في الشريط العلوي"""
    if current_user.get('username'):
        username = current_user['username']
        role = current_user['role']
        role_name = "مدير النظام" if role == 'admin' else "مستخدم محدود"
        user_info_label.config(text=f"👤 {username} ({role_name})")
    else:
        user_info_label.config(text="👤 غير مسجل دخول")

# ==== تسجيل الخروج ====
def logout():
    # مسح معلومات المستخدم الحالي
    current_user['username'] = ''
    current_user['role'] = ''
    
    # مسح حقول تسجيل الدخول
    username_entry.delete(0, tk.END)
    username_entry.insert(0, "اسم المستخدم")
    username_entry.config(fg='gray')
    
    password_entry.delete(0, tk.END)
    password_entry.insert(0, "كلمة المرور")
    password_entry.config(fg='gray')
    
    # إخفاء الشريط العلوي والقائمة الجانبية
    top_bar.pack_forget()
    dash_frame.pack_forget()
    
    # تحديث معلومات المستخدم في الشريط العلوي
    update_user_info()
    
    # رسالة تأكيد تسجيل الخروج
    messagebox.showinfo("تسجيل خروج", "تم تسجيل الخروج بنجاح")
    
    # إظهار شاشة تسجيل الدخول
    login_frame.pack(fill="both", expand=True)

def create_placeholder_entry(parent, placeholder_text, show_char=None):
    """إنشاء حقل إدخال مع نص توضيحي"""
    entry = tk.Entry(parent, font=FONTS['main'], width=25, relief="solid", bd=1, show=show_char)
    entry.pack(pady=15)
    
    # إضافة placeholder
    entry.insert(0, placeholder_text)
    entry.config(fg='gray')
    
    def on_focus_in(event):
        if entry.get() == placeholder_text:
            entry.delete(0, tk.END)
            entry.config(fg='black')
    
    def on_focus_out(event):
        if entry.get() == '':
            entry.insert(0, placeholder_text)
            entry.config(fg='gray')
    
    entry.bind('<FocusIn>', on_focus_in)
    entry.bind('<FocusOut>', on_focus_out)
    
    return entry

# ==== نافذة التطبيق ====
root = tk.Tk()
root.title("🏥 صيدلية الشفاء - نظام إدارة شامل")
root.geometry("1200x700")
root.configure(bg=COLORS['bg_main'])

# ==== واجهة تسجيل الدخول المحسنة ====
login_frame = tk.Frame(root, bg=COLORS['bg_main'])

# إطار تسجيل الدخول المركزي
login_container = tk.Frame(login_frame, bg=COLORS['bg_card'], relief="raised", bd=3)
login_container.place(relx=0.5, rely=0.5, anchor="center", width=400, height=350)

# شعار وعنوان
tk.Label(login_container, text=f"{ICONS['pharmacy']} صيدلية الشفاء",
         font=FONTS['title'], bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=15)

tk.Label(login_container, text="نظام إدارة شامل ومتطور",
         font=FONTS['main'], bg=COLORS['bg_card'], fg=COLORS['text_secondary']).pack(pady=5)

tk.Label(login_container, text="تسجيل الدخول",
         font=FONTS['heading'], bg=COLORS['bg_card'], fg=COLORS['text_secondary']).pack(pady=10)

# حقول الإدخال
username_entry = create_placeholder_entry(login_container, "اسم المستخدم")
password_entry = create_placeholder_entry(login_container, "كلمة المرور", "*")

# ربط Enter بتسجيل الدخول
username_entry.bind("<Return>", lambda event: password_entry.focus())
password_entry.bind("<Return>", lambda event: login())

# زر تسجيل الدخول
login_btn = tk.Button(login_container, text=f"{ICONS['user']} دخول", command=login,
                     bg=COLORS['btn_success'], fg=COLORS['text_white'],
                     font=FONTS['button'], width=20, height=2, relief="flat", cursor="hand2")
login_btn.pack(pady=20)

# ==== الشريط العلوي لمعلومات المستخدم (مخفي في البداية) ====
top_bar = tk.Frame(root, bg=COLORS['primary'], height=40)
top_bar.pack_propagate(False)

# معلومات المستخدم الحالي
user_info_label = tk.Label(top_bar, text="👤 غير مسجل دخول", 
                          font=FONTS['main'], bg=COLORS['primary'], fg=COLORS['text_white'])
user_info_label.pack(side="right", padx=20, pady=8)

# عنوان النظام
system_title = tk.Label(top_bar, text="🏥 صيدلية الشفاء - نظام إدارة شامل", 
                       font=FONTS['heading'], bg=COLORS['primary'], fg=COLORS['text_white'])
system_title.pack(side="left", padx=20, pady=8)

# ==== قائمة جانبية محسنة للوحة التحكم (مخفية في البداية) ====
dash_frame = tk.Frame(root, bg=COLORS['bg_sidebar'], width=220)
dash_frame.pack_propagate(False)

# شعار النظام في الأعلى
logo_frame = tk.Frame(dash_frame, bg=COLORS['bg_sidebar'], height=80)
logo_frame.pack(fill="x", pady=10)
logo_frame.pack_propagate(False)

tk.Label(logo_frame, text=f"{ICONS['pharmacy']}", font=('Arial', 28),
         bg=COLORS['bg_sidebar'], fg=COLORS['text_white']).pack()
tk.Label(logo_frame, text="صيدلية الشفاء", font=('Arial', 14, 'bold'),
         bg=COLORS['bg_sidebar'], fg=COLORS['text_white']).pack()

# أزرار القائمة
menu_buttons = [
    ("📊 لوحة التحكم", show_dashboard),
    ("🚪 تسجيل خروج", logout)
]

for text, command in menu_buttons:
    btn = tk.Button(dash_frame, text=text, command=command,
                   bg=COLORS['bg_sidebar'], fg=COLORS['text_white'],
                   font=FONTS['main'], relief="flat", anchor="w", pady=8)
    btn.pack(fill="x", pady=2, padx=5)

# ==== المحتوى الرئيسي ====
main_content = tk.Frame(root, bg=COLORS['bg_main'])

# ==== بدء النظام بشاشة تسجيل الدخول فقط ====
login_frame.pack(fill="both", expand=True)

print("🚀 تم تشغيل النظام المبسط")
print("📝 بيانات تسجيل الدخول:")
print("   المدير: admin / admin123")
print("   المستخدم: user / user123")

root.mainloop()
